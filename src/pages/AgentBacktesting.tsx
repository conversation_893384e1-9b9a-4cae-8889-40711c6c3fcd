import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Loader2, BarChart3, TrendingUp, TrendingDown, DollarSign } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { getAgentsByUserId } from '@/services/agentService';
import { supabase } from '@/integrations/supabase/client';
import PortfolioChart from '@/components/portfolio/PortfolioChart';

interface Agent {
  id: string;
  name: string;
  description: string;
  configuration: any;
}

interface BacktestResult {
  symbol: string;
  startDate: string;
  endDate: string;
  totalReturn: number;
  buyAndHoldReturn: number;
  numberOfTrades: number;
  winRate: number;
  maxDrawdown: number;
  sharpeRatio: number;
  trades: Array<{
    date: string;
    type: 'buy' | 'sell';
    price: number;
    signal: string;
    confidence: number;
  }>;
  performanceChart: Array<{
    date: string;
    agentValue: number;
    buyHoldValue: number;
  }>;
}

const AgentBacktesting: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  const [agents, setAgents] = useState<Agent[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  const [selectedSymbol, setSelectedSymbol] = useState<string>('AAPL');
  const [selectedTimeframe, setSelectedTimeframe] = useState<string>('1Y');
  const [selectedInterval, setSelectedInterval] = useState<string>('1D');
  const [isBacktesting, setIsBacktesting] = useState(false);
  const [backtestResult, setBacktestResult] = useState<BacktestResult | null>(null);
  const [loadingAgents, setLoadingAgents] = useState(true);
  const [displayedTitle, setDisplayedTitle] = useState('');
  const [showCursor, setShowCursor] = useState(true);

  // Get current date for reference
  const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

  // Timeframe options (how much historical data to test)
  const timeframes = [
    { value: '3M', label: '3 Months' },
    { value: '6M', label: '6 Months' },
    { value: '1Y', label: '1 Year' },
    { value: '2Y', label: '2 Years' },
    { value: '5Y', label: '5 Years' }
  ];

  // Interval options (how often the agent runs)
  const intervals = [
    { value: '1D', label: 'Daily' },
    { value: '1W', label: 'Weekly' },
    { value: '1M', label: 'Monthly' }
  ];

  // Typing animation for title
  useEffect(() => {
    const title = "Agent Backtesting";
    let i = 0;
    const typingInterval = setInterval(() => {
      if (i < title.length) {
        setDisplayedTitle(title.substring(0, i + 1));
        i++;
      } else {
        clearInterval(typingInterval);
        // Hide cursor after typing is complete
        setTimeout(() => setShowCursor(false), 500);
      }
    }, 100); // 100ms per character for smooth typing

    return () => clearInterval(typingInterval);
  }, []);

  // Load user's agents
  useEffect(() => {
    const loadAgents = async () => {
      if (!user?.id) return;

      try {
        const userAgents = await getAgentsByUserId(user.id);
        setAgents(userAgents);
      } catch (error) {
        console.error('Error loading agents:', error);
        toast({
          title: 'Error',
          description: 'Failed to load your agents',
          variant: 'destructive'
        });
      } finally {
        setLoadingAgents(false);
      }
    };

    loadAgents();
  }, [user?.id, toast]);

  // Run backtest
  const handleBacktest = async () => {
    if (!selectedAgent || !selectedSymbol) {
      toast({
        title: 'Missing Information',
        description: 'Please select an agent and enter a stock symbol',
        variant: 'destructive'
      });
      return;
    }

    setIsBacktesting(true);
    setBacktestResult(null);

    try {
      // Call the agent backtesting edge function
      const { data, error } = await supabase.functions.invoke('agent-backtesting', {
        body: {
          agentId: selectedAgent,
          symbol: selectedSymbol.toUpperCase(),
          timeframe: selectedTimeframe,
          interval: selectedInterval,
          userId: user?.id,
          currentDate: currentDate // Pass current date for consistent calculations
        }
      });

      if (error) {
        throw error;
      }

      console.log('Backtest result:', data); // Debug log
      setBacktestResult(data);

      toast({
        title: 'Backtest Complete',
        description: `Analyzed ${data.numberOfTrades} trades over ${selectedTimeframe}`,
      });
    } catch (error) {
      console.error('Error running backtest:', error);
      toast({
        title: 'Backtest Failed',
        description: 'Failed to run backtest. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsBacktesting(false);
    }
  };

  if (loadingAgents) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex items-center justify-center font-hanken-grotesk">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-white/60" />
          <p className="text-white/60 text-sm">Loading your agents...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-[#0A0A0A] text-white flex flex-col font-hanken-grotesk">
      {/* Header & Subtle Configuration */}
      <div className="px-8 py-6 border-b border-white/[0.04]">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-3 mb-2">
              <BarChart3 className="h-5 w-5 text-white/60" />
              <h1 className="text-xl font-medium text-white">
                {displayedTitle}
                {showCursor && <span className="animate-pulse">|</span>}
              </h1>
            </div>
            <p className="text-white/50 text-xs">
              Test your trading agents against historical data
            </p>
          </div>

          {/* Compact Configuration */}
          <div className="flex items-center gap-3">
            <Select value={selectedAgent} onValueChange={setSelectedAgent}>
              <SelectTrigger className="bg-white/[0.02] border-white/[0.04] text-white h-8 text-xs w-32">
                <SelectValue placeholder="Agent" />
              </SelectTrigger>
              <SelectContent className="bg-[#1A1A1A] border-white/[0.08]">
                {agents.map((agent) => (
                  <SelectItem key={agent.id} value={agent.id} className="text-white hover:bg-white/[0.05] text-xs">
                    {agent.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Input
              value={selectedSymbol}
              onChange={(e) => setSelectedSymbol(e.target.value.toUpperCase())}
              placeholder="AAPL"
              className="bg-white/[0.02] border-white/[0.04] text-white h-8 text-xs uppercase placeholder-white/40 w-20"
            />

            <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
              <SelectTrigger className="bg-white/[0.02] border-white/[0.04] text-white h-8 text-xs w-24">
                <SelectValue placeholder="Period" />
              </SelectTrigger>
              <SelectContent className="bg-[#1A1A1A] border-white/[0.08]">
                {timeframes.map((timeframe) => (
                  <SelectItem key={timeframe.value} value={timeframe.value} className="text-white hover:bg-white/[0.05] text-xs">
                    {timeframe.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button
              onClick={handleBacktest}
              disabled={!selectedAgent || !selectedSymbol || isBacktesting}
              className="bg-white/[0.06] hover:bg-white/[0.08] border border-white/[0.04] text-white/80 hover:text-white h-8 px-4 text-xs font-medium"
            >
              {isBacktesting ? (
                <>
                  <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                  Testing...
                </>
              ) : (
                'Test'
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Backtest Results */}
      {backtestResult && (
        <div className="flex-1 overflow-y-auto">
          {/* Performance Chart - First and Prominent */}
          <div className="px-8 pt-6 pb-4">
            <div className="bg-[#0D0D0D] border border-[#1A1A1A]/30 rounded-lg overflow-hidden shadow-[0_8px_32px_rgba(0,0,0,0.4),inset_0_1px_0_rgba(255,255,255,0.05)]" style={{ height: '480px' }}>
              <PortfolioChart
                data={backtestResult.performanceChart && backtestResult.performanceChart.length > 0
                  ? backtestResult.performanceChart.map(item => ({
                      date: item.date,
                      value: item.agentValue
                    }))
                  : [
                      { date: backtestResult.startDate, value: 10000 },
                      { date: backtestResult.endDate, value: 10000 * (1 + backtestResult.totalReturn / 100) }
                    ]
                }
                title=""
                height={480}
                loading={false}
              />
            </div>
          </div>

          {/* Performance Metrics - Small Cards */}
          <div className="px-8 pb-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <div className="bg-white/[0.02] border border-white/[0.04] rounded p-3 text-center">
                <div className={`text-lg font-medium ${
                  backtestResult.totalReturn > 0 ? 'text-emerald-400' : 'text-red-400'
                }`}>
                  {backtestResult.totalReturn > 0 ? '+' : ''}{backtestResult.totalReturn.toFixed(2)}%
                </div>
                <div className="text-xs text-white/60 mt-1">Agent Return</div>
              </div>

              <div className="bg-white/[0.02] border border-white/[0.04] rounded p-3 text-center">
                <div className="text-lg font-medium text-white">{backtestResult.numberOfTrades}</div>
                <div className="text-xs text-white/60 mt-1">Total Trades</div>
              </div>

              <div className="bg-white/[0.02] border border-white/[0.04] rounded p-3 text-center">
                <div className="text-lg font-medium text-white">
                  {backtestResult.winRate.toFixed(1)}%
                </div>
                <div className="text-xs text-white/60 mt-1">Win Rate</div>
              </div>

              <div className="bg-white/[0.02] border border-white/[0.04] rounded p-3 text-center">
                <div className="text-lg font-medium text-white">
                  -{backtestResult.maxDrawdown.toFixed(2)}%
                </div>
                <div className="text-xs text-white/60 mt-1">Max Drawdown</div>
              </div>
            </div>
          </div>

          {/* Trade History - Clean List */}
          <div className="px-8 pb-6">
            <div className="mb-3">
              <h3 className="text-sm font-medium text-white/80">Trade History</h3>
              <p className="text-xs text-white/50">Recent trades from your agent</p>
            </div>
            <div className="space-y-1 max-h-[300px] overflow-y-auto">
              {backtestResult.trades.slice().reverse().map((trade, index) => (
                <div key={index} className="flex items-center justify-between py-2 px-3 hover:bg-white/[0.02] rounded transition-colors">
                  <div className="flex items-center gap-3">
                    <span className={`w-2 h-2 rounded-full ${
                      trade.type === 'buy' ? 'bg-emerald-400' : 'bg-red-400'
                    }`} />
                    <span className="text-xs text-white/60 font-mono w-20">{trade.date}</span>
                    <span className={`text-xs font-medium ${
                      trade.type === 'buy' ? 'text-emerald-400' : 'text-red-400'
                    }`}>
                      {trade.type.toUpperCase()}
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="text-xs text-white/70">${trade.price.toFixed(2)}</span>
                    <span className="text-xs text-white/50">
                      {trade.signal} ({trade.confidence}%)
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AgentBacktesting;
