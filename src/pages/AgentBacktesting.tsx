import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Loader2, BarChart3, TrendingUp, TrendingDown, DollarSign } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { getAgentsByUserId } from '@/services/agentService';
import { supabase } from '@/integrations/supabase/client';

interface Agent {
  id: string;
  name: string;
  description: string;
  configuration: any;
}

interface BacktestResult {
  symbol: string;
  startDate: string;
  endDate: string;
  totalReturn: number;
  buyAndHoldReturn: number;
  numberOfTrades: number;
  winRate: number;
  maxDrawdown: number;
  sharpeRatio: number;
  trades: Array<{
    date: string;
    type: 'buy' | 'sell';
    price: number;
    signal: string;
    confidence: number;
  }>;
  performanceChart: Array<{
    date: string;
    agentValue: number;
    buyHoldValue: number;
  }>;
}

const AgentBacktesting: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  const [agents, setAgents] = useState<Agent[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  const [selectedSymbol, setSelectedSymbol] = useState<string>('AAPL');
  const [selectedTimeframe, setSelectedTimeframe] = useState<string>('1Y');
  const [selectedInterval, setSelectedInterval] = useState<string>('1D');
  const [isBacktesting, setIsBacktesting] = useState(false);
  const [backtestResult, setBacktestResult] = useState<BacktestResult | null>(null);
  const [loadingAgents, setLoadingAgents] = useState(true);

  // Get current date for reference
  const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

  // Timeframe options (how much historical data to test)
  const timeframes = [
    { value: '3M', label: '3 Months' },
    { value: '6M', label: '6 Months' },
    { value: '1Y', label: '1 Year' },
    { value: '2Y', label: '2 Years' },
    { value: '5Y', label: '5 Years' }
  ];

  // Interval options (how often the agent runs)
  const intervals = [
    { value: '1D', label: 'Daily' },
    { value: '1W', label: 'Weekly' },
    { value: '1M', label: 'Monthly' }
  ];

  // Load user's agents
  useEffect(() => {
    const loadAgents = async () => {
      if (!user?.id) return;

      try {
        const userAgents = await getAgentsByUserId(user.id);
        setAgents(userAgents);
      } catch (error) {
        console.error('Error loading agents:', error);
        toast({
          title: 'Error',
          description: 'Failed to load your agents',
          variant: 'destructive'
        });
      } finally {
        setLoadingAgents(false);
      }
    };

    loadAgents();
  }, [user?.id, toast]);

  // Run backtest
  const handleBacktest = async () => {
    if (!selectedAgent || !selectedSymbol) {
      toast({
        title: 'Missing Information',
        description: 'Please select an agent and enter a stock symbol',
        variant: 'destructive'
      });
      return;
    }

    setIsBacktesting(true);
    setBacktestResult(null);

    try {
      // Call the agent backtesting edge function
      const { data, error } = await supabase.functions.invoke('agent-backtesting', {
        body: {
          agentId: selectedAgent,
          symbol: selectedSymbol.toUpperCase(),
          timeframe: selectedTimeframe,
          interval: selectedInterval,
          userId: user?.id,
          currentDate: currentDate // Pass current date for consistent calculations
        }
      });

      if (error) {
        throw error;
      }

      setBacktestResult(data);

      toast({
        title: 'Backtest Complete',
        description: `Analyzed ${data.numberOfTrades} trades over ${selectedTimeframe}`,
      });
    } catch (error) {
      console.error('Error running backtest:', error);
      toast({
        title: 'Backtest Failed',
        description: 'Failed to run backtest. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsBacktesting(false);
    }
  };

  if (loadingAgents) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-3">
        <BarChart3 className="h-6 w-6 text-primary" />
        <h1 className="text-3xl font-bold">Agent Backtesting</h1>
      </div>

      <p className="text-muted-foreground">
        Test your trading agents against historical data to evaluate their performance.
      </p>

      {/* Backtest Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Backtest Configuration</CardTitle>
          <div className="text-sm text-muted-foreground">
            Current Date: <span className="font-mono font-medium">{currentDate}</span>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="agent">Select Agent</Label>
              <Select value={selectedAgent} onValueChange={setSelectedAgent}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose an agent" />
                </SelectTrigger>
                <SelectContent>
                  {agents.map((agent) => (
                    <SelectItem key={agent.id} value={agent.id}>
                      {agent.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="symbol">Stock Symbol</Label>
              <Input
                id="symbol"
                value={selectedSymbol}
                onChange={(e) => setSelectedSymbol(e.target.value.toUpperCase())}
                placeholder="e.g., AAPL"
                className="uppercase"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="timeframe">Backtest Period</Label>
              <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
                <SelectTrigger>
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  {timeframes.map((timeframe) => (
                    <SelectItem key={timeframe.value} value={timeframe.value}>
                      {timeframe.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="interval">Trading Frequency</Label>
              <Select value={selectedInterval} onValueChange={setSelectedInterval}>
                <SelectTrigger>
                  <SelectValue placeholder="Select frequency" />
                </SelectTrigger>
                <SelectContent>
                  {intervals.map((interval) => (
                    <SelectItem key={interval.value} value={interval.value}>
                      {interval.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="text-sm text-muted-foreground bg-muted/30 p-3 rounded-lg">
            <strong>Trading Logic:</strong> Bullish = Buy/Long • Bearish = Sell/Short • Neutral = Exit All Positions
          </div>

          <Button
            onClick={handleBacktest}
            disabled={!selectedAgent || !selectedSymbol || isBacktesting}
            className="w-full md:w-auto"
          >
            {isBacktesting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Running Backtest...
              </>
            ) : (
              <>
                <BarChart3 className="mr-2 h-4 w-4" />
                Run Backtest
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Backtest Results */}
      {backtestResult && (
        <div className="space-y-6">
          {/* Performance Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Performance Summary</CardTitle>
              <div className="text-sm text-muted-foreground">
                Backtest Period: <span className="font-mono">{backtestResult.startDate}</span> to <span className="font-mono">{backtestResult.endDate}</span>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="flex items-center justify-center mb-2">
                    <DollarSign className="h-5 w-5 text-green-500" />
                  </div>
                  <div className="text-2xl font-bold text-green-600">
                    {backtestResult.totalReturn > 0 ? '+' : ''}{backtestResult.totalReturn.toFixed(2)}%
                  </div>
                  <div className="text-sm text-muted-foreground">Agent Return</div>
                </div>

                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="flex items-center justify-center mb-2">
                    <TrendingUp className="h-5 w-5 text-blue-500" />
                  </div>
                  <div className="text-2xl font-bold text-blue-600">
                    {backtestResult.buyAndHoldReturn > 0 ? '+' : ''}{backtestResult.buyAndHoldReturn.toFixed(2)}%
                  </div>
                  <div className="text-sm text-muted-foreground">Buy & Hold</div>
                </div>

                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold">{backtestResult.numberOfTrades}</div>
                  <div className="text-sm text-muted-foreground">Total Trades</div>
                </div>

                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {backtestResult.winRate.toFixed(1)}%
                  </div>
                  <div className="text-sm text-muted-foreground">Win Rate</div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-xl font-bold text-red-600">
                    -{backtestResult.maxDrawdown.toFixed(2)}%
                  </div>
                  <div className="text-sm text-muted-foreground">Max Drawdown</div>
                </div>

                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-xl font-bold text-orange-600">
                    {backtestResult.sharpeRatio.toFixed(2)}
                  </div>
                  <div className="text-sm text-muted-foreground">Sharpe Ratio</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Trade History */}
          <Card>
            <CardHeader>
              <CardTitle>Trade History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-[600px] overflow-y-auto">
                {backtestResult.trades.slice().reverse().map((trade, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge variant={trade.type === 'buy' ? 'default' : 'secondary'}>
                        {trade.type.toUpperCase()}
                      </Badge>
                      <span className="text-sm text-muted-foreground">{trade.date}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="font-medium">${trade.price.toFixed(2)}</span>
                      <Badge variant="outline" className="text-xs">
                        {trade.signal} ({trade.confidence}%)
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default AgentBacktesting;
