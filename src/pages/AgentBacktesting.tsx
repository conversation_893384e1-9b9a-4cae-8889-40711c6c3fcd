import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Loader2, BarChart3, TrendingUp, TrendingDown, DollarSign } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { getAgentsByUserId } from '@/services/agentService';
import { supabase } from '@/integrations/supabase/client';
import ReactECharts from 'echarts-for-react';

interface Agent {
  id: string;
  name: string;
  description: string;
  configuration: any;
}

interface BacktestResult {
  symbol: string;
  startDate: string;
  endDate: string;
  totalReturn: number;
  buyAndHoldReturn: number;
  numberOfTrades: number;
  winRate: number;
  maxDrawdown: number;
  sharpeRatio: number;
  trades: Array<{
    date: string;
    type: 'buy' | 'sell';
    price: number;
    signal: string;
    confidence: number;
  }>;
  performanceChart: Array<{
    date: string;
    agentValue: number;
    buyHoldValue: number;
  }>;
}

const AgentBacktesting: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  const [agents, setAgents] = useState<Agent[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  const [selectedSymbol, setSelectedSymbol] = useState<string>('AAPL');
  const [selectedTimeframe, setSelectedTimeframe] = useState<string>('1Y');
  const [selectedInterval, setSelectedInterval] = useState<string>('1D');
  const [isBacktesting, setIsBacktesting] = useState(false);
  const [backtestResult, setBacktestResult] = useState<BacktestResult | null>(null);
  const [loadingAgents, setLoadingAgents] = useState(true);
  const [displayedTitle, setDisplayedTitle] = useState('');
  const [showCursor, setShowCursor] = useState(true);

  // Get current date for reference
  const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

  // Timeframe options (how much historical data to test)
  const timeframes = [
    { value: '3M', label: '3 Months' },
    { value: '6M', label: '6 Months' },
    { value: '1Y', label: '1 Year' },
    { value: '2Y', label: '2 Years' },
    { value: '5Y', label: '5 Years' }
  ];

  // Interval options (how often the agent runs)
  const intervals = [
    { value: '1D', label: 'Daily' },
    { value: '1W', label: 'Weekly' },
    { value: '1M', label: 'Monthly' }
  ];

  // Typing animation for title
  useEffect(() => {
    const title = "Agent Backtesting";
    let i = 0;
    const typingInterval = setInterval(() => {
      if (i < title.length) {
        setDisplayedTitle(title.substring(0, i + 1));
        i++;
      } else {
        clearInterval(typingInterval);
        // Hide cursor after typing is complete
        setTimeout(() => setShowCursor(false), 500);
      }
    }, 100); // 100ms per character for smooth typing

    return () => clearInterval(typingInterval);
  }, []);

  // Load user's agents
  useEffect(() => {
    const loadAgents = async () => {
      if (!user?.id) return;

      try {
        const userAgents = await getAgentsByUserId(user.id);
        setAgents(userAgents);
      } catch (error) {
        console.error('Error loading agents:', error);
        toast({
          title: 'Error',
          description: 'Failed to load your agents',
          variant: 'destructive'
        });
      } finally {
        setLoadingAgents(false);
      }
    };

    loadAgents();
  }, [user?.id, toast]);

  // Run backtest
  const handleBacktest = async () => {
    if (!selectedAgent || !selectedSymbol) {
      toast({
        title: 'Missing Information',
        description: 'Please select an agent and enter a stock symbol',
        variant: 'destructive'
      });
      return;
    }

    setIsBacktesting(true);
    setBacktestResult(null);

    try {
      // Call the agent backtesting edge function
      const { data, error } = await supabase.functions.invoke('agent-backtesting', {
        body: {
          agentId: selectedAgent,
          symbol: selectedSymbol.toUpperCase(),
          timeframe: selectedTimeframe,
          interval: selectedInterval,
          userId: user?.id,
          currentDate: currentDate // Pass current date for consistent calculations
        }
      });

      if (error) {
        throw error;
      }

      setBacktestResult(data);

      toast({
        title: 'Backtest Complete',
        description: `Analyzed ${data.numberOfTrades} trades over ${selectedTimeframe}`,
      });
    } catch (error) {
      console.error('Error running backtest:', error);
      toast({
        title: 'Backtest Failed',
        description: 'Failed to run backtest. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsBacktesting(false);
    }
  };

  if (loadingAgents) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex items-center justify-center font-hanken-grotesk">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-white/60" />
          <p className="text-white/60 text-sm">Loading your agents...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-[#0A0A0A] text-white flex flex-col font-hanken-grotesk">
      {/* Header Section */}
      <div className="px-8 py-6">
        <div className="flex items-center gap-3 mb-3">
          <BarChart3 className="h-6 w-6 text-white/60" />
          <h1 className="text-2xl font-bold text-white">
            {displayedTitle}
            {showCursor && <span className="animate-pulse">|</span>}
          </h1>
        </div>
        <p className="text-white/50 text-sm">
          Test your trading agents against historical data to evaluate their performance
        </p>
      </div>

      {/* Backtest Configuration */}
      <div className="px-8 pb-6">
        <div className="bg-white/[0.02] border border-white/[0.06] rounded-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-sm font-medium text-white">Backtest Configuration</h2>
            <div className="text-xs text-white/50">
              Current Date: <span className="font-mono text-white/70">{currentDate}</span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="space-y-3">
              <label className="text-sm font-medium text-white/80">Select Agent</label>
              <Select value={selectedAgent} onValueChange={setSelectedAgent}>
                <SelectTrigger className="bg-white/[0.02] border-white/[0.06] text-white h-10 text-sm">
                  <SelectValue placeholder="Choose an agent" />
                </SelectTrigger>
                <SelectContent className="bg-[#1A1A1A] border-white/[0.08]">
                  {agents.map((agent) => (
                    <SelectItem key={agent.id} value={agent.id} className="text-white hover:bg-white/[0.05] text-sm">
                      {agent.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {agents.length === 0 && (
                <p className="text-xs text-white/50">
                  No agents found. Create an agent first in the Agent Builder.
                </p>
              )}
            </div>

            <div className="space-y-3">
              <label className="text-sm font-medium text-white/80">Stock Symbol</label>
              <Input
                value={selectedSymbol}
                onChange={(e) => setSelectedSymbol(e.target.value.toUpperCase())}
                placeholder="e.g., AAPL"
                className="bg-white/[0.02] border-white/[0.06] text-white h-10 text-sm uppercase placeholder-white/40"
              />
            </div>

            <div className="space-y-3">
              <label className="text-sm font-medium text-white/80">Backtest Period</label>
              <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
                <SelectTrigger className="bg-white/[0.02] border-white/[0.06] text-white h-10 text-sm">
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent className="bg-[#1A1A1A] border-white/[0.08]">
                  {timeframes.map((timeframe) => (
                    <SelectItem key={timeframe.value} value={timeframe.value} className="text-white hover:bg-white/[0.05] text-sm">
                      {timeframe.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-3">
              <label className="text-sm font-medium text-white/80">Trading Frequency</label>
              <Select value={selectedInterval} onValueChange={setSelectedInterval}>
                <SelectTrigger className="bg-white/[0.02] border-white/[0.06] text-white h-10 text-sm">
                  <SelectValue placeholder="Select frequency" />
                </SelectTrigger>
                <SelectContent className="bg-[#1A1A1A] border-white/[0.08]">
                  {intervals.map((interval) => (
                    <SelectItem key={interval.value} value={interval.value} className="text-white hover:bg-white/[0.05] text-sm">
                      {interval.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="text-xs text-white/50 bg-white/[0.02] p-3 rounded border border-white/[0.04] mt-6">
            <span className="text-white/70 font-medium">Trading Logic:</span> Bullish = Buy/Long • Bearish = Sell/Short • Neutral = Exit All Positions
          </div>

          <div className="flex justify-center pt-6">
            <Button
              onClick={handleBacktest}
              disabled={!selectedAgent || !selectedSymbol || isBacktesting}
              className="bg-white/[0.06] hover:bg-white/[0.08] border border-white/[0.05] text-white/80 hover:text-white shadow-[inset_0_2px_6px_rgba(0,0,0,0.15),inset_0_1px_0_rgba(255,255,255,0.1)] px-6 py-2 text-sm font-medium"
            >
              {isBacktesting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Running Backtest...
                </>
              ) : (
                <>
                  <BarChart3 className="mr-2 h-4 w-4" />
                  Run Backtest
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Backtest Results */}
      {backtestResult && (
        <div className="flex-1 px-8 pb-6">
          {/* Performance Metrics */}
          <div className="bg-white/[0.02] border border-white/[0.06] rounded-lg p-6 mb-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-sm font-medium text-white">Performance Summary</h2>
              <div className="text-xs text-white/50">
                Period: <span className="font-mono text-white/70">{backtestResult.startDate}</span> to <span className="font-mono text-white/70">{backtestResult.endDate}</span>
              </div>
            </div>

            <div className="space-y-4">
              {/* Primary Metrics Row */}
              <div className="flex items-center justify-between py-3 border-b border-white/[0.06]">
                <div className="flex items-center gap-3">
                  <span className="text-sm text-white/70">Agent Return</span>
                </div>
                <div className={`text-lg font-medium ${
                  backtestResult.totalReturn > 0 ? 'text-emerald-400' : 'text-red-400'
                }`}>
                  {backtestResult.totalReturn > 0 ? '+' : ''}{backtestResult.totalReturn.toFixed(2)}%
                </div>
              </div>

              <div className="flex items-center justify-between py-3 border-b border-white/[0.06]">
                <div className="flex items-center gap-3">
                  <span className="text-sm text-white/70">Buy & Hold Return</span>
                </div>
                <div className={`text-lg font-medium ${
                  backtestResult.buyAndHoldReturn > 0 ? 'text-emerald-400' : 'text-red-400'
                }`}>
                  {backtestResult.buyAndHoldReturn > 0 ? '+' : ''}{backtestResult.buyAndHoldReturn.toFixed(2)}%
                </div>
              </div>

              {/* Secondary Metrics Row */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 pt-2">
                <div className="text-center">
                  <div className="text-base font-medium text-white">{backtestResult.numberOfTrades}</div>
                  <div className="text-xs text-white/60 mt-1">Total Trades</div>
                </div>

                <div className="text-center">
                  <div className="text-base font-medium text-white">
                    {backtestResult.winRate.toFixed(1)}%
                  </div>
                  <div className="text-xs text-white/60 mt-1">Win Rate</div>
                </div>

                <div className="text-center">
                  <div className="text-base font-medium text-white">
                    -{backtestResult.maxDrawdown.toFixed(2)}%
                  </div>
                  <div className="text-xs text-white/60 mt-1">Max Drawdown</div>
                </div>

                <div className="text-center">
                  <div className="text-base font-medium text-white">
                    {backtestResult.sharpeRatio.toFixed(2)}
                  </div>
                  <div className="text-xs text-white/60 mt-1">Sharpe Ratio</div>
                </div>
              </div>
            </div>
          </div>

          {/* Performance Chart */}
          {backtestResult.performanceChart && backtestResult.performanceChart.length > 0 && (
            <div className="bg-[#0D0D0D] border border-[#1A1A1A]/30 rounded-lg overflow-hidden shadow-sm mb-6" style={{ height: 'min(400px, 50vh)' }}>
              <ReactECharts
                option={{
                  backgroundColor: 'transparent',
                  grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '10%',
                    containLabel: true
                  },
                  tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(20, 20, 20, 0.95)',
                    borderColor: 'rgba(255, 255, 255, 0.1)',
                    borderWidth: 1,
                    textStyle: {
                      color: '#ffffff',
                      fontSize: 12
                    },
                    formatter: function(params: any) {
                      const date = params[0].axisValue;
                      const value = params[0].value.toFixed(2);
                      return `<div style="margin-bottom: 4px; font-weight: 500;">${date}</div>
                        <div style="display: flex; align-items: center;">
                          <span style="display: inline-block; width: 8px; height: 8px; background-color: ${params[0].color}; border-radius: 50%; margin-right: 6px;"></span>
                          <span style="margin-right: 8px;">Portfolio Value:</span>
                          <span style="font-weight: 500;">$${value}</span>
                        </div>`;
                    }
                  },
                  legend: {
                    show: false
                  },
                  xAxis: {
                    type: 'category',
                    data: backtestResult.performanceChart.map(item => item.date),
                    axisLine: {
                      lineStyle: {
                        color: 'rgba(255, 255, 255, 0.1)'
                      }
                    },
                    axisLabel: {
                      color: 'rgba(255, 255, 255, 0.6)',
                      fontSize: 10
                    },
                    splitLine: {
                      show: false
                    }
                  },
                  yAxis: {
                    type: 'value',
                    axisLine: {
                      lineStyle: {
                        color: 'rgba(255, 255, 255, 0.1)'
                      }
                    },
                    axisLabel: {
                      color: 'rgba(255, 255, 255, 0.6)',
                      fontSize: 10,
                      formatter: '${value}'
                    },
                    splitLine: {
                      lineStyle: {
                        color: 'rgba(255, 255, 255, 0.05)'
                      }
                    }
                  },
                  series: [
                    {
                      name: 'Agent Performance',
                      data: backtestResult.performanceChart.map(item => item.agentValue),
                      type: 'line',
                      smooth: true,
                      symbol: 'none',
                      lineStyle: {
                        width: 3,
                        color: backtestResult.totalReturn >= 0 ? 'rgba(78, 184, 151, 0.9)' : 'rgba(229, 128, 128, 0.9)'
                      },
                      areaStyle: {
                        color: {
                          type: 'linear',
                          x: 0,
                          y: 0,
                          x2: 0,
                          y2: 1,
                          colorStops: [{
                            offset: 0,
                            color: backtestResult.totalReturn >= 0 ? 'rgba(78, 184, 151, 0.3)' : 'rgba(229, 128, 128, 0.3)'
                          }, {
                            offset: 1,
                            color: 'rgba(0, 0, 0, 0)'
                          }]
                        }
                      }
                    }
                  ]
                }}
                style={{ height: '100%', width: '100%' }}
                className="bg-[#0D0D0D] rounded-md"
              />
            </div>
          )}

          {/* Trade History */}
          <div className="bg-white/[0.02] border border-white/[0.06] rounded-lg">
            <div className="p-6 pb-4 border-b border-white/[0.06]">
              <h2 className="text-sm font-medium text-white">Trade History</h2>
            </div>
            <div className="p-6">
              <div className="space-y-2 max-h-[400px] overflow-y-auto">
                {backtestResult.trades.slice().reverse().map((trade, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-white/[0.02] border border-white/[0.04] rounded hover:bg-white/[0.03] transition-colors">
                    <div className="flex items-center gap-3">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        trade.type === 'buy'
                          ? 'bg-emerald-500/15 text-emerald-300 border border-emerald-500/30'
                          : 'bg-red-500/15 text-red-300 border border-red-500/30'
                      }`}>
                        {trade.type.toUpperCase()}
                      </span>
                      <span className="text-sm text-white/60 font-mono">{trade.date}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="font-medium text-white text-sm">${trade.price.toFixed(2)}</span>
                      <span className="bg-white/[0.06] text-white/70 border border-white/[0.08] text-xs px-2 py-1 rounded">
                        {trade.signal} ({trade.confidence}%)
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AgentBacktesting;
