import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  MessageCircle, 
  TrendingUp, 
  Briefcase, 
  Hammer, 
  BarChart3,
  ArrowRight,
  Sparkles,
  Bot,
  Search
} from 'lucide-react';
import { WelcomeHeading } from '@/components/ui/WelcomeHeading';

const Home: React.FC = () => {
  const navigate = useNavigate();

  const features = [
    {
      title: 'AI Chat Interface',
      description: 'Get instant market insights and trading advice from our advanced AI',
      icon: <MessageCircle className="w-8 h-8 text-blue-400" />,
      path: '/chat',
      color: 'from-blue-500/10 to-blue-600/5'
    },
    {
      title: 'Stock Scanner',
      description: 'Discover trending stocks and market opportunities',
      icon: <TrendingUp className="w-8 h-8 text-green-400" />,
      path: '/stock-scanner',
      color: 'from-green-500/10 to-green-600/5'
    },
    {
      title: 'Portfolio Builder',
      description: 'Build and manage your investment portfolio with AI assistance',
      icon: <Briefcase className="w-8 h-8 text-purple-400" />,
      path: '/portfolio-builder',
      color: 'from-purple-500/10 to-purple-600/5'
    },
    {
      title: 'Agent Builder',
      description: 'Create custom trading agents with our visual builder',
      icon: <Hammer className="w-8 h-8 text-orange-400" />,
      path: '/agent-builder',
      color: 'from-orange-500/10 to-orange-600/5'
    }
  ];

  const quickActions = [
    {
      title: 'Trading Agents',
      description: 'Manage your AI trading agents',
      icon: <Bot className="w-5 h-5" />,
      path: '/agents'
    },
    {
      title: 'Agent Scanner',
      description: 'Discover and analyze trading strategies',
      icon: <Search className="w-5 h-5" />,
      path: '/agent-scanner'
    },
    {
      title: 'Market Analysis',
      description: 'Get comprehensive market insights',
      icon: <BarChart3 className="w-5 h-5" />,
      path: '/chat'
    }
  ];

  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="mb-12">
          <WelcomeHeading
            text="Welcome to Osis"
            className="text-4xl font-bold mb-4 bg-gradient-to-r from-white via-white/90 to-white/70 bg-clip-text text-transparent"
            speed={80}
          />
          <p className="text-xl text-white/60 max-w-2xl">
            Your AI-powered trading companion. Analyze markets, build portfolios, and create intelligent trading agents.
          </p>
        </div>

        {/* Main Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {features.map((feature, index) => (
            <Card 
              key={index}
              className="bg-gradient-to-br from-[#1A1A1A]/90 to-[#141414]/80 border border-white/[0.08] hover:border-white/20 transition-all duration-300 cursor-pointer group"
              onClick={() => navigate(feature.path)}
            >
              <CardHeader className="pb-4">
                <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${feature.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  {feature.icon}
                </div>
                <CardTitle className="text-lg font-semibold text-white/90 group-hover:text-white transition-colors">
                  {feature.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-white/60 text-sm leading-relaxed mb-4">
                  {feature.description}
                </p>
                <Button 
                  variant="ghost" 
                  size="sm"
                  className="w-full justify-between text-white/70 hover:text-white hover:bg-white/5 transition-all duration-200"
                >
                  Get Started
                  <ArrowRight className="w-4 h-4" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions Section */}
        <div className="mb-12">
          <h2 className="text-2xl font-semibold mb-6 text-white/90">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {quickActions.map((action, index) => (
              <Card 
                key={index}
                className="bg-[#1A1A1A]/60 border border-white/[0.06] hover:border-white/15 transition-all duration-300 cursor-pointer group"
                onClick={() => navigate(action.path)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-lg bg-white/[0.04] flex items-center justify-center group-hover:bg-white/[0.08] transition-colors">
                      {action.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-white/90 group-hover:text-white transition-colors">
                        {action.title}
                      </h3>
                      <p className="text-xs text-white/50">
                        {action.description}
                      </p>
                    </div>
                    <ArrowRight className="w-4 h-4 text-white/40 group-hover:text-white/70 transition-colors" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Getting Started Section */}
        <Card className="bg-gradient-to-r from-emerald-500/10 via-blue-500/10 to-purple-500/10 border border-white/[0.08]">
          <CardContent className="p-8">
            <div className="flex items-center gap-4 mb-4">
              <div className="w-12 h-12 rounded-full bg-gradient-to-r from-emerald-400 to-blue-400 flex items-center justify-center">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-white/90">New to Osis?</h3>
                <p className="text-white/60">Start with our AI chat to get personalized trading insights</p>
              </div>
            </div>
            <Button 
              onClick={() => navigate('/chat')}
              className="bg-gradient-to-r from-emerald-500 to-blue-500 hover:from-emerald-600 hover:to-blue-600 text-white border-0"
            >
              Start Chatting
              <MessageCircle className="w-4 h-4 ml-2" />
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Home;
