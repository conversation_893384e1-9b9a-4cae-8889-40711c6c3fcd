import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Loader2, Search, TrendingUp, AlertCircle } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { getAgentsByUserId } from '@/services/agentService';
import { supabase } from '@/integrations/supabase/client';

interface Agent {
  id: string;
  name: string;
  description: string;
  configuration: any;
}

interface ScanResult {
  symbol: string;
  signal: string;
  confidence: number;
  price: number;
  change: number;
  percentChange: number;
}

const AgentScanner: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [agents, setAgents] = useState<Agent[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  const [selectedIndex, setSelectedIndex] = useState<string>('sp500');
  const [isScanning, setIsScanning] = useState(false);
  const [scanResults, setScanResults] = useState<ScanResult[]>([]);
  const [loadingAgents, setLoadingAgents] = useState(true);

  // Market indices options
  const marketIndices = [
    { value: 'sp500', label: 'S&P 500' },
    { value: 'nasdaq', label: 'NASDAQ Composite' },
    { value: 'nasdaq100', label: 'NASDAQ 100' },
    { value: 'russell2000', label: 'Russell 2000' },
    { value: 'all', label: 'All Stocks' }
  ];

  // Load user's agents
  useEffect(() => {
    const loadAgents = async () => {
      if (!user?.id) return;
      
      try {
        const userAgents = await getAgentsByUserId(user.id);
        setAgents(userAgents);
      } catch (error) {
        console.error('Error loading agents:', error);
        toast({
          title: 'Error',
          description: 'Failed to load your agents',
          variant: 'destructive'
        });
      } finally {
        setLoadingAgents(false);
      }
    };

    loadAgents();
  }, [user?.id, toast]);

  // Run agent scanner
  const handleScan = async () => {
    if (!selectedAgent || !selectedIndex) {
      toast({
        title: 'Missing Selection',
        description: 'Please select both an agent and market index',
        variant: 'destructive'
      });
      return;
    }

    setIsScanning(true);
    setScanResults([]);

    try {
      // Call the agent scanner edge function
      const { data, error } = await supabase.functions.invoke('agent-scanner', {
        body: {
          agentId: selectedAgent,
          marketIndex: selectedIndex,
          userId: user?.id
        }
      });

      if (error) {
        throw error;
      }

      setScanResults(data.results || []);
      
      toast({
        title: 'Scan Complete',
        description: `Found ${data.results?.length || 0} bullish signals`,
      });
    } catch (error) {
      console.error('Error running agent scanner:', error);
      toast({
        title: 'Scan Failed',
        description: 'Failed to run agent scanner. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsScanning(false);
    }
  };

  if (loadingAgents) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-3">
        <Search className="h-6 w-6 text-primary" />
        <h1 className="text-3xl font-bold">Agent Scanner</h1>
      </div>
      
      <p className="text-muted-foreground">
        Run your trading agents against market data to find stocks that match your criteria.
      </p>

      {/* Scanner Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Scanner Configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Select Agent</label>
              <Select value={selectedAgent} onValueChange={setSelectedAgent}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose an agent to run" />
                </SelectTrigger>
                <SelectContent>
                  {agents.map((agent) => (
                    <SelectItem key={agent.id} value={agent.id}>
                      {agent.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {agents.length === 0 && (
                <p className="text-sm text-muted-foreground">
                  No agents found. Create an agent first in the Agent Builder.
                </p>
              )}
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Market Scope</label>
              <Select value={selectedIndex} onValueChange={setSelectedIndex}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose market index" />
                </SelectTrigger>
                <SelectContent>
                  {marketIndices.map((index) => (
                    <SelectItem key={index.value} value={index.value}>
                      {index.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <Button 
            onClick={handleScan} 
            disabled={!selectedAgent || !selectedIndex || isScanning}
            className="w-full md:w-auto"
          >
            {isScanning ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Scanning...
              </>
            ) : (
              <>
                <Search className="mr-2 h-4 w-4" />
                Run Scanner
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Scan Results */}
      {scanResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-500" />
              Bullish Signals Found ({scanResults.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {scanResults.map((result, index) => (
                <Card key={index} className="border-l-4 border-l-green-500">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-semibold text-lg">{result.symbol}</h3>
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        {result.confidence}% confidence
                      </Badge>
                    </div>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Price:</span>
                        <span className="font-medium">${result.price.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Change:</span>
                        <span className={`font-medium ${result.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {result.change >= 0 ? '+' : ''}${result.change.toFixed(2)} ({result.percentChange.toFixed(2)}%)
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Signal:</span>
                        <Badge variant="outline" className="text-green-600 border-green-600">
                          {result.signal}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* No Results Message */}
      {!isScanning && scanResults.length === 0 && selectedAgent && selectedIndex && (
        <Card>
          <CardContent className="p-8 text-center">
            <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Signals Found</h3>
            <p className="text-muted-foreground">
              Your agent didn't find any bullish signals in the selected market index. 
              Try adjusting your agent's criteria or selecting a different market scope.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AgentScanner;
