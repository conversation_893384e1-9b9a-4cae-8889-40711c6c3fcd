import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Loader2,
  Search,
  TrendingUp,
  AlertCircle,
  RefreshCw,
  ArrowUpRight,
  ArrowDownRight,
  BarChart3,
  Activity,
  Zap,
  Target,
  Brain,
  CheckCircle,
  Clock,
  Wifi,
  Database,
  Signal,
  Eye,
  ChevronDown,
  ChevronUp,
  TrendingDown
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { getAgentsByUserId } from '@/services/agentService';
import { supabase } from '@/integrations/supabase/client';
import { WelcomeHeading } from '@/components/ui/WelcomeHeading';

interface Agent {
  id: string;
  name: string;
  description: string;
  configuration: any;
}

interface ScanResult {
  symbol: string;
  signal: string;
  confidence: number;
  price: number;
  change: number;
  percentChange: number;
}

interface ScanningPhase {
  id: string;
  label: string;
  icon: React.ReactNode;
  completed: boolean;
  active: boolean;
}

const AgentScanner: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  const [agents, setAgents] = useState<Agent[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  const [selectedIndex, setSelectedIndex] = useState<string>('sp500');
  const [isScanning, setIsScanning] = useState(false);
  const [scanResults, setScanResults] = useState<ScanResult[]>([]);
  const [loadingAgents, setLoadingAgents] = useState(true);
  const [scanningPhases, setScanningPhases] = useState<ScanningPhase[]>([]);
  const [currentPhase, setCurrentPhase] = useState(0);
  const [progress, setProgress] = useState(0);
  const [expandedCard, setExpandedCard] = useState<number | null>(null);
  const [showResults, setShowResults] = useState(false);

  // Market indices options with enhanced data
  const marketIndices = [
    {
      value: 'sp500',
      label: 'S&P 500',
      description: '500 largest US companies',
      icon: <BarChart3 className="h-4 w-4" />,
      status: 'active'
    },
    {
      value: 'nasdaq',
      label: 'NASDAQ Composite',
      description: 'All NASDAQ listed stocks',
      icon: <TrendingUp className="h-4 w-4" />,
      status: 'active'
    },
    {
      value: 'nasdaq100',
      label: 'NASDAQ 100',
      description: 'Top 100 non-financial NASDAQ stocks',
      icon: <Target className="h-4 w-4" />,
      status: 'active'
    },
    {
      value: 'russell2000',
      label: 'Russell 2000',
      description: 'Small-cap US stocks',
      icon: <Activity className="h-4 w-4" />,
      status: 'active'
    },
    {
      value: 'all',
      label: 'All Stocks',
      description: 'Entire market universe',
      icon: <Database className="h-4 w-4" />,
      status: 'active'
    }
  ];

  // Initialize scanning phases
  const initializeScanningPhases = (): ScanningPhase[] => [
    {
      id: 'connect',
      label: 'Connecting to agent...',
      icon: <Wifi className="h-4 w-4" />,
      completed: false,
      active: false
    },
    {
      id: 'analyze',
      label: 'Analyzing market data...',
      icon: <Database className="h-4 w-4" />,
      completed: false,
      active: false
    },
    {
      id: 'process',
      label: 'Processing signals...',
      icon: <Brain className="h-4 w-4" />,
      completed: false,
      active: false
    },
    {
      id: 'complete',
      label: 'Scan complete',
      icon: <CheckCircle className="h-4 w-4" />,
      completed: false,
      active: false
    }
  ];

  // Load user's agents
  useEffect(() => {
    const loadAgents = async () => {
      if (!user?.id) return;

      try {
        const userAgents = await getAgentsByUserId(user.id);
        setAgents(userAgents);
      } catch (error) {
        console.error('Error loading agents:', error);
        toast({
          title: 'Error',
          description: 'Failed to load your agents',
          variant: 'destructive'
        });
      } finally {
        setLoadingAgents(false);
      }
    };

    loadAgents();
  }, [user?.id, toast]);

  // Enhanced scan function with phases
  const handleScan = async () => {
    if (!selectedAgent || !selectedIndex) {
      toast({
        title: 'Missing Selection',
        description: 'Please select both an agent and market index',
        variant: 'destructive'
      });
      return;
    }

    setIsScanning(true);
    setScanResults([]);
    setShowResults(false);
    setProgress(0);
    setCurrentPhase(0);

    const phases = initializeScanningPhases();
    setScanningPhases(phases);

    try {
      // Phase 1: Connecting to agent
      await simulatePhase(0, 'Connecting to agent...');

      // Phase 2: Analyzing market data
      await simulatePhase(1, 'Analyzing market data...');

      // Phase 3: Processing signals
      await simulatePhase(2, 'Processing signals...');

      // Call the agent scanner edge function
      const { data, error } = await supabase.functions.invoke('agent-scanner', {
        body: {
          agentId: selectedAgent,
          marketIndex: selectedIndex,
          userId: user?.id
        }
      });

      if (error) {
        throw error;
      }

      // Phase 4: Complete
      await simulatePhase(3, 'Scan complete');

      // Show results with staggered animation
      setScanResults(data.results || []);
      setTimeout(() => setShowResults(true), 500);

      toast({
        title: 'Scan Complete',
        description: `Found ${data.results?.length || 0} bullish signals`,
      });
    } catch (error) {
      console.error('Error running agent scanner:', error);
      toast({
        title: 'Scan Failed',
        description: 'Failed to run agent scanner. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setTimeout(() => {
        setIsScanning(false);
        setProgress(100);
      }, 1000);
    }
  };

  // Simulate scanning phases with progress
  const simulatePhase = async (phaseIndex: number, label: string) => {
    setCurrentPhase(phaseIndex);

    // Update phases state
    setScanningPhases(prev => prev.map((phase, index) => ({
      ...phase,
      active: index === phaseIndex,
      completed: index < phaseIndex
    })));

    // Simulate progress for this phase
    const phaseProgress = (phaseIndex / 4) * 100;
    const nextPhaseProgress = ((phaseIndex + 1) / 4) * 100;

    for (let i = phaseProgress; i <= nextPhaseProgress; i += 2) {
      setProgress(i);
      await new Promise(resolve => setTimeout(resolve, 50));
    }

    // Mark phase as completed
    setScanningPhases(prev => prev.map((phase, index) => ({
      ...phase,
      completed: index <= phaseIndex,
      active: false
    })));
  };

  // Utility functions
  const formatNumber = (num: number): string => {
    if (num >= 1e9) return `$${(num / 1e9).toFixed(2)}B`;
    if (num >= 1e6) return `$${(num / 1e6).toFixed(2)}M`;
    if (num >= 1e3) return `$${(num / 1e3).toFixed(2)}K`;
    return `$${num.toFixed(2)}`;
  };

  const getSelectedAgentData = () => {
    return agents.find(agent => agent.id === selectedAgent);
  };

  const getSelectedMarketData = () => {
    return marketIndices.find(market => market.value === selectedIndex);
  };

  if (loadingAgents) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <div className="relative">
            <div className="w-16 h-16 border-4 border-white/10 border-t-emerald-400 rounded-full animate-spin"></div>
            <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-t-emerald-600 rounded-full animate-spin" style={{ animationDelay: '0.5s' }}></div>
          </div>
          <div className="text-center">
            <p className="text-white/80 text-lg font-medium">Loading Trading Agents</p>
            <p className="text-white/50 text-sm mt-1">Initializing your trading arsenal...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-[#0A0A0A] text-white flex flex-col relative overflow-hidden">
      {/* Ambient Background Effects */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-0 left-0 w-96 h-96 bg-emerald-500/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      {/* Header Section */}
      <div className="relative z-10 px-8 py-8 border-b border-white/[0.08]">
        <div className="flex items-center justify-between">
          <div>
            <WelcomeHeading
              text="Agent Scanner"
              className="text-3xl font-bold text-white mb-3"
              speed={80}
            />
            <p className="text-white/60 text-lg">
              Deploy your AI trading agents to scan the market for opportunities
            </p>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 px-4 py-2 bg-white/[0.03] border border-white/[0.08] rounded-lg">
              <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
              <span className="text-white/70 text-sm">Market Active</span>
            </div>
            <Button
              onClick={() => window.location.reload()}
              className="bg-white/[0.06] hover:bg-white/[0.08] border border-white/[0.05] text-white/70 hover:text-white shadow-[inset_0_2px_6px_rgba(0,0,0,0.15),inset_0_1px_0_rgba(255,255,255,0.1)]"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>
      </div>

      {/* Configuration Dashboard */}
      <div className="flex-1 relative z-10 px-8 py-8">
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8 h-full">

          {/* Agent Selection Panel */}
          <div className="space-y-6">
            <div className="flex items-center gap-3 mb-6">
              <Brain className="h-6 w-6 text-emerald-400" />
              <h2 className="text-xl font-semibold text-white">Select Trading Agent</h2>
            </div>

            {agents.length === 0 ? (
              <Card className="bg-white/[0.02] border-white/[0.08] border-l-4 border-l-amber-500">
                <CardContent className="p-6">
                  <div className="flex items-center gap-3">
                    <AlertCircle className="h-5 w-5 text-amber-400" />
                    <div>
                      <h3 className="text-white font-medium">No Agents Available</h3>
                      <p className="text-white/60 text-sm mt-1">Create an agent first in the Agent Builder to start scanning.</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="grid gap-4">
                {agents.map((agent) => (
                  <Card
                    key={agent.id}
                    onClick={() => setSelectedAgent(agent.id)}
                    className={`cursor-pointer transition-all duration-300 ${
                      selectedAgent === agent.id
                        ? 'bg-white/[0.08] border-emerald-500/30 shadow-[inset_0_2px_6px_rgba(0,0,0,0.15),inset_0_1px_0_rgba(255,255,255,0.1)] border-2'
                        : 'bg-white/[0.02] border-white/[0.08] hover:bg-white/[0.04] hover:border-white/[0.12]'
                    }`}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                            selectedAgent === agent.id ? 'bg-emerald-500/20' : 'bg-white/[0.05]'
                          }`}>
                            <Brain className={`h-6 w-6 ${
                              selectedAgent === agent.id ? 'text-emerald-400' : 'text-white/60'
                            }`} />
                          </div>
                          <div>
                            <h3 className="text-white font-semibold text-lg">{agent.name}</h3>
                            <p className="text-white/60 text-sm mt-1">{agent.description || 'Custom trading strategy'}</p>
                          </div>
                        </div>
                        <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                          selectedAgent === agent.id
                            ? 'border-emerald-400 bg-emerald-400'
                            : 'border-white/30'
                        }`}>
                          {selectedAgent === agent.id && (
                            <CheckCircle className="h-4 w-4 text-white" />
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* Market Scope Panel */}
          <div className="space-y-6">
            <div className="flex items-center gap-3 mb-6">
              <Target className="h-6 w-6 text-blue-400" />
              <h2 className="text-xl font-semibold text-white">Market Scope</h2>
            </div>

            <div className="grid gap-4">
              {marketIndices.map((market) => (
                <Card
                  key={market.value}
                  onClick={() => setSelectedIndex(market.value)}
                  className={`cursor-pointer transition-all duration-300 ${
                    selectedIndex === market.value
                      ? 'bg-white/[0.08] border-blue-500/30 shadow-[inset_0_2px_6px_rgba(0,0,0,0.15),inset_0_1px_0_rgba(255,255,255,0.1)] border-2'
                      : 'bg-white/[0.02] border-white/[0.08] hover:bg-white/[0.04] hover:border-white/[0.12]'
                  }`}
                >
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                          selectedIndex === market.value ? 'bg-blue-500/20' : 'bg-white/[0.05]'
                        }`}>
                          <div className={`${
                            selectedIndex === market.value ? 'text-blue-400' : 'text-white/60'
                          }`}>
                            {market.icon}
                          </div>
                        </div>
                        <div>
                          <h3 className="text-white font-semibold text-lg">{market.label}</h3>
                          <p className="text-white/60 text-sm mt-1">{market.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className={`w-2 h-2 rounded-full ${
                          market.status === 'active' ? 'bg-emerald-400' : 'bg-red-400'
                        } animate-pulse`}></div>
                        <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                          selectedIndex === market.value
                            ? 'border-blue-400 bg-blue-400'
                            : 'border-white/30'
                        }`}>
                          {selectedIndex === market.value && (
                            <CheckCircle className="h-4 w-4 text-white" />
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Scan Control Panel */}
        <div className="mt-8 flex justify-center">
          <div className="bg-white/[0.02] border border-white/[0.08] rounded-2xl p-8 backdrop-blur-sm">
            <div className="text-center mb-6">
              <h3 className="text-white text-lg font-semibold mb-2">Ready to Deploy</h3>
              <p className="text-white/60 text-sm">
                {selectedAgent && selectedIndex
                  ? `Deploy ${getSelectedAgentData()?.name} on ${getSelectedMarketData()?.label}`
                  : 'Select an agent and market scope to begin scanning'
                }
              </p>
            </div>

            <Button
              onClick={handleScan}
              disabled={!selectedAgent || !selectedIndex || isScanning}
              className={`px-12 py-4 text-lg font-semibold transition-all duration-300 ${
                isScanning
                  ? 'bg-emerald-600/50 cursor-not-allowed'
                  : 'bg-emerald-600 hover:bg-emerald-700 hover:scale-105'
              }`}
              size="lg"
            >
              {isScanning ? (
                <div className="flex items-center gap-3">
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  <span>Scanning Market...</span>
                </div>
              ) : (
                <div className="flex items-center gap-3">
                  <Zap className="h-5 w-5" />
                  <span>Deploy Agent Scanner</span>
                </div>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Scanning Progress Overlay */}
      {isScanning && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-white/[0.02] border border-white/[0.08] rounded-2xl p-8 max-w-md w-full mx-4">
            <div className="text-center mb-8">
              <div className="relative w-24 h-24 mx-auto mb-6">
                <div className="absolute inset-0 border-4 border-white/10 rounded-full"></div>
                <div
                  className="absolute inset-0 border-4 border-transparent border-t-emerald-400 rounded-full animate-spin"
                  style={{ animationDuration: '1s' }}
                ></div>
                <div
                  className="absolute inset-2 border-4 border-transparent border-t-emerald-600 rounded-full animate-spin"
                  style={{ animationDuration: '1.5s', animationDirection: 'reverse' }}
                ></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <Brain className="h-8 w-8 text-emerald-400" />
                </div>
              </div>

              <h3 className="text-white text-xl font-semibold mb-2">Agent Scanning Market</h3>
              <p className="text-white/60 text-sm mb-6">
                {scanningPhases[currentPhase]?.label || 'Initializing...'}
              </p>

              {/* Progress Bar */}
              <div className="w-full bg-white/10 rounded-full h-2 mb-4">
                <div
                  className="bg-gradient-to-r from-emerald-400 to-emerald-600 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>

              <p className="text-white/40 text-xs">{Math.round(progress)}% Complete</p>
            </div>

            {/* Scanning Phases */}
            <div className="space-y-3">
              {scanningPhases.map((phase, index) => (
                <div
                  key={phase.id}
                  className={`flex items-center gap-3 p-3 rounded-lg transition-all duration-300 ${
                    phase.completed
                      ? 'bg-emerald-500/10 border border-emerald-500/20'
                      : phase.active
                        ? 'bg-white/[0.05] border border-white/[0.1]'
                        : 'bg-white/[0.02] border border-white/[0.05]'
                  }`}
                >
                  <div className={`flex-shrink-0 ${
                    phase.completed
                      ? 'text-emerald-400'
                      : phase.active
                        ? 'text-white animate-pulse'
                        : 'text-white/40'
                  }`}>
                    {phase.completed ? <CheckCircle className="h-4 w-4" /> : phase.icon}
                  </div>
                  <span className={`text-sm ${
                    phase.completed
                      ? 'text-emerald-300'
                      : phase.active
                        ? 'text-white'
                        : 'text-white/60'
                  }`}>
                    {phase.label}
                  </span>
                  {phase.active && (
                    <div className="ml-auto">
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Results Display */}
      {showResults && scanResults.length > 0 && (
        <div className="fixed inset-0 bg-black/90 backdrop-blur-sm z-40 overflow-y-auto">
          <div className="min-h-full p-8">
            <div className="max-w-6xl mx-auto">
              {/* Results Header */}
              <div className="text-center mb-8">
                <div className="flex items-center justify-center gap-3 mb-4">
                  <TrendingUp className="h-8 w-8 text-emerald-400" />
                  <h2 className="text-3xl font-bold text-white">Bullish Signals Detected</h2>
                </div>
                <p className="text-white/60 text-lg">
                  Found {scanResults.length} trading opportunities matching your agent's criteria
                </p>
                <Button
                  onClick={() => setShowResults(false)}
                  className="mt-4 bg-white/[0.06] hover:bg-white/[0.08] border border-white/[0.05] text-white/70 hover:text-white"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Back to Scanner
                </Button>
              </div>

              {/* Results Grid */}
              <div className="grid gap-6">
                {scanResults.map((result, index) => (
                  <Card
                    key={index}
                    className={`bg-white/[0.02] border-white/[0.08] hover:bg-white/[0.04] transition-all duration-500 border-l-4 border-l-emerald-500 ${
                      showResults ? 'animate-in slide-in-from-bottom-4' : ''
                    }`}
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <CardContent className="p-8">
                      <div className="flex items-center justify-between">
                        {/* Stock Info */}
                        <div className="flex items-center gap-6">
                          <div className="w-16 h-16 bg-emerald-500/10 rounded-xl flex items-center justify-center">
                            <TrendingUp className="h-8 w-8 text-emerald-400" />
                          </div>
                          <div>
                            <div className="flex items-center gap-3 mb-2">
                              <h3 className="text-2xl font-bold text-white">{result.symbol}</h3>
                              <Badge className="bg-emerald-500/20 text-emerald-300 border-emerald-500/30 px-3 py-1">
                                {result.confidence}% confidence
                              </Badge>
                            </div>
                            <p className="text-white/60 text-lg">{result.signal}</p>
                          </div>
                        </div>

                        {/* Price and Actions */}
                        <div className="text-right">
                          <div className="text-3xl font-bold text-white mb-2">${result.price.toFixed(2)}</div>
                          <div className={`flex items-center justify-end gap-2 text-lg mb-4 ${
                            result.change >= 0 ? 'text-emerald-400' : 'text-red-400'
                          }`}>
                            {result.change >= 0 ? (
                              <ArrowUpRight className="h-5 w-5" />
                            ) : (
                              <ArrowDownRight className="h-5 w-5" />
                            )}
                            ${Math.abs(result.change).toFixed(2)} ({result.percentChange >= 0 ? '+' : ''}{result.percentChange.toFixed(2)}%)
                          </div>

                          <div className="flex gap-3">
                            <Button
                              onClick={() => setExpandedCard(expandedCard === index ? null : index)}
                              className="bg-white/[0.06] hover:bg-white/[0.08] border border-white/[0.05] text-white/70 hover:text-white"
                            >
                              {expandedCard === index ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                              Details
                            </Button>
                            <Button className="bg-emerald-600 hover:bg-emerald-700 text-white">
                              <Activity className="h-4 w-4 mr-2" />
                              Analyze
                            </Button>
                          </div>
                        </div>
                      </div>

                      {/* Expanded Details */}
                      {expandedCard === index && (
                        <div className="mt-8 pt-8 border-t border-white/[0.08] animate-in slide-in-from-top-2">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div className="bg-white/[0.02] rounded-lg p-4">
                              <h4 className="text-white font-medium mb-2">Signal Strength</h4>
                              <div className="w-full bg-white/10 rounded-full h-2">
                                <div
                                  className="bg-emerald-400 h-2 rounded-full"
                                  style={{ width: `${result.confidence}%` }}
                                ></div>
                              </div>
                              <p className="text-white/60 text-sm mt-2">{result.confidence}% confidence</p>
                            </div>
                            <div className="bg-white/[0.02] rounded-lg p-4">
                              <h4 className="text-white font-medium mb-2">Price Action</h4>
                              <p className="text-white/60 text-sm">Current: ${result.price.toFixed(2)}</p>
                              <p className="text-white/60 text-sm">Change: {result.change >= 0 ? '+' : ''}${result.change.toFixed(2)}</p>
                            </div>
                            <div className="bg-white/[0.02] rounded-lg p-4">
                              <h4 className="text-white font-medium mb-2">Agent Signal</h4>
                              <p className="text-white/60 text-sm">{result.signal}</p>
                            </div>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* No Results State */}
      {!isScanning && scanResults.length === 0 && selectedAgent && selectedIndex && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-40 flex items-center justify-center">
          <Card className="bg-white/[0.02] border-white/[0.08] max-w-md w-full mx-4">
            <CardContent className="p-8 text-center">
              <AlertCircle className="h-16 w-16 text-white/40 mx-auto mb-6" />
              <h3 className="text-xl font-semibold mb-4 text-white">No Signals Found</h3>
              <p className="text-white/60 mb-6">
                Your agent didn't find any bullish signals in the selected market index.
                Try adjusting your agent's criteria or selecting a different market scope.
              </p>
              <Button
                onClick={() => {
                  setScanResults([]);
                  setSelectedAgent('');
                  setSelectedIndex('sp500');
                }}
                className="bg-emerald-600 hover:bg-emerald-700 text-white"
              >
                Try Different Configuration
              </Button>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default AgentScanner;
