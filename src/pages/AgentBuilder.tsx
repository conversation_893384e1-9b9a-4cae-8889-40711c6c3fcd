import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import { Save, Play, ArrowLeft, Trash2, Sparkles, Settings, Circle, PenSquare, ChevronRight, ChevronLeft } from 'lucide-react';
import { useAgentBuilder } from '@/hooks/useAgentBuilder';
import { getAgentById, saveAgent, runAgent } from '@/services/agentService';
import { useAuth } from '@/contexts/AuthContext';
import { WelcomeHeading } from '@/components/ui/WelcomeHeading';
import BlockPalette from '@/components/agent-builder/BlockPalette';
import BuildCanvas from '@/components/agent-builder/BuildCanvas';
import AgentRunDialog from '@/components/agent-builder/AgentRunDialog';
import AIAgentBuilder from '@/components/agent-builder/AIAgentBuilder';

// Add custom animation styles for the gradient heading
const animationStyles = `
  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .gradient-text {
    background: linear-gradient(90deg, rgba(255,255,255,0.95), rgba(200,200,200,0.8), rgba(170,170,170,0.9));
    background-size: 200% auto;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient 8s ease infinite;
  }
`;

const AgentBuilder: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { isAuthenticated, user } = useAuth();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [isRunDialogOpen, setIsRunDialogOpen] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('ai');
  const [isPaletteExpanded, setIsPaletteExpanded] = useState<boolean>(false);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (isAuthenticated === false) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to access the agent builder',
        variant: 'destructive'
      });
      navigate('/auth');
    }
  }, [isAuthenticated, navigate, toast]);

  // Initialize agent builder hook
  const {
    blocks,
    connections,
    entryBlockId,
    agentName,
    agentDescription,
    setAgentName,
    setAgentDescription,
    addBlock,
    updateBlock,
    removeBlock,
    addConnection,
    removeConnection,
    setEntryBlock,
    clearBuilder,
    loadAgent,
    getAgentConfiguration,
    validateAgent,
    hasChanges,
    resetChanges
  } = useAgentBuilder();

  // Load agent if ID is provided
  useEffect(() => {
    if (id) {
      setIsLoading(true);
      getAgentById(id)
        .then(agent => {
          if (agent) {
            loadAgent(agent);
          } else {
            toast({
              title: 'Error',
              description: 'Agent not found',
              variant: 'destructive'
            });
            navigate('/agents');
          }
        })
        .catch(error => {
          console.error('Error loading agent:', error);
          toast({
            title: 'Error',
            description: 'Failed to load agent',
            variant: 'destructive'
          });
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [id, loadAgent, navigate, toast]);

  // Validate agent and get error details
  const { valid, errors, errorDetails, disconnectedBlocks } = validateAgent();

  // Handle save
  const handleSave = async () => {
    // Check if user is authenticated
    if (!isAuthenticated || !user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to save your agent',
        variant: 'destructive'
      });
      navigate('/auth');
      return;
    }

    // Validate agent
    const { valid, errors } = validateAgent();
    if (!valid) {
      toast({
        title: 'Validation Error',
        description: errors.join(', '),
        variant: 'destructive'
      });
      return;
    }

    // Save agent
    setIsSaving(true);
    try {
      const agentConfig = getAgentConfiguration();
      const savedAgent = await saveAgent({
        ...agentConfig,
        id
      });

      toast({
        title: 'Success',
        description: 'Agent saved successfully'
      });

      // Reset changes flag
      resetChanges();

      // Navigate to the agent page if this is a new agent
      if (!id) {
        navigate(`/agent-builder/${savedAgent.id}`);
      }
    } catch (error: any) {
      console.error('Error saving agent:', error);

      // Handle authentication errors
      if (error.message === 'User not authenticated') {
        toast({
          title: 'Authentication Required',
          description: 'Please log in to save your agent',
          variant: 'destructive'
        });
        navigate('/auth');
      } else {
        toast({
          title: 'Error',
          description: error.message || 'Failed to save agent',
          variant: 'destructive'
        });
      }
    } finally {
      setIsSaving(false);
    }
  };

  // Autosave functionality
  useEffect(() => {
    if (!hasChanges || isSaving || !isAuthenticated || !user) return;

    // Debounce the autosave to avoid too many requests
    const autosaveTimeout = setTimeout(async () => {
      // Only autosave if the agent is valid
      const { valid } = validateAgent();
      if (!valid) return;

      // Don't autosave if we don't have an ID yet
      if (!id) return;

      setIsSaving(true);
      try {
        const agentConfig = getAgentConfiguration();
        await saveAgent({
          ...agentConfig,
          id
        });

        // Reset changes flag
        resetChanges();

        console.log('Agent autosaved');
      } catch (error: any) {
        console.error('Error autosaving agent:', error);

        // If authentication error, don't keep trying to autosave
        if (error.message === 'User not authenticated') {
          // We'll let the user know when they try to manually save
          console.log('Autosave disabled: User not authenticated');
        }
      } finally {
        setIsSaving(false);
      }
    }, 2000); // 2 second debounce

    return () => clearTimeout(autosaveTimeout);
  }, [hasChanges, id, isSaving, isAuthenticated, user, getAgentConfiguration, validateAgent, resetChanges]);

  // Handle run
  const handleRun = () => {
    // Check if user is authenticated
    if (!isAuthenticated || !user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to run your agent',
        variant: 'destructive'
      });
      navigate('/auth');
      return;
    }

    // Validate agent
    const { valid, errors } = validateAgent();
    if (!valid) {
      toast({
        title: 'Validation Error',
        description: errors.join(', '),
        variant: 'destructive'
      });
      return;
    }

    // Open run dialog
    setIsRunDialogOpen(true);
  };

  // Handle block drop
  const handleBlockDrop = (type: string, position: { x: number; y: number }, properties?: Record<string, any>) => {
    addBlock(type, position, properties);
  };

  // Handle AI agent generation
  const handleAIAgentGenerated = (aiAgent: {
    blocks: any[];
    entryBlockId: string;
    name: string;
    description: string;
  }) => {
    // Clear existing agent
    clearBuilder();

    // Load the AI-generated agent
    loadAgent({
      name: aiAgent.name,
      description: aiAgent.description,
      configuration: {
        blocks: aiAgent.blocks,
        entryBlockId: aiAgent.entryBlockId
      }
    });

    toast({
      title: 'AI Agent Generated',
      description: `Successfully created "${aiAgent.name}". Switch to the Design tab to see the visual layout.`
    });
  };

  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white flex flex-col">
      <style>{animationStyles}</style>

      {/* Header Section - Fixed layout */}
      <div className="flex items-start justify-between px-4 pt-2 pb-3 border-b border-[#1A1A1A]/30">
        <div className="flex items-start gap-3">
          <button
            onClick={() => navigate('/agents')}
            className="text-white/60 hover:text-white transition-colors duration-200 mt-1"
          >
            <ArrowLeft className="h-4 w-4" />
          </button>
          <div className="flex flex-col">
            <WelcomeHeading
              text={agentName || 'Build Your Agent'}
              className="gradient-text text-lg font-semibold leading-tight"
              speed={80}
            />
            {agentDescription && (
              <p className="text-xs text-white/60 mt-0.5">{agentDescription}</p>
            )}
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden bg-[#0A0A0A]">
        <ResizablePanelGroup direction="horizontal" className="h-full">
          {/* Block Palette Sidebar - Only show on Design Canvas tab */}
          {activeTab === 'canvas' && (
            <>
              <ResizablePanel
                defaultSize={30}
                minSize={20}
                maxSize={40}
                className="relative"
              >
                <div className="h-full bg-[#0A0A0A] border-r border-[#1A1A1A]/30 overflow-y-auto custom-scrollbar">
                  <div className="p-4">
                    {/* Toggle switch positioned in left corner above Block Palette */}
                    <div className="relative bg-[#0A0A0A] border-b border-[#1A1A1A]/30">
                      <div className="flex items-center justify-start py-3 px-2">
                        <div className="bg-[#0A0A0A] border border-[#1A1A1A]/30 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] rounded-full p-0.5 flex">
                          <button
                            onClick={() => setActiveTab('ai')}
                            className={`flex items-center gap-1.5 ${activeTab === 'ai' ? 'bg-[#141414] text-white shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)]' : ''} rounded-full px-3 py-1.5 transition-all duration-300 text-sm`}
                          >
                            <Sparkles className="h-3.5 w-3.5" />
                            Build with AI
                          </button>
                          <button
                            onClick={() => setActiveTab('canvas')}
                            className={`flex items-center gap-1.5 ${activeTab === 'canvas' ? 'bg-[#141414] text-white shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)]' : ''} rounded-full px-3 py-1.5 transition-all duration-300 text-sm`}
                          >
                            <Settings className="h-3.5 w-3.5" />
                            Design Canvas
                          </button>
                        </div>
                      </div>
                      {/* Subtle bottom glow */}
                      <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/10 to-transparent"></div>
                    </div>
                    <BlockPalette onBlockDrop={handleBlockDrop} />
                  </div>
                </div>
              </ResizablePanel>
              <ResizableHandle withHandle className="bg-[#1A1A1A]/30 hover:bg-[#1A1A1A]/50 w-2" />
            </>
          )}

          {/* Main Canvas Area */}
          <ResizablePanel defaultSize={activeTab === 'canvas' ? 70 : 100}>
            <Tabs
              value={activeTab}
              className="h-full flex flex-col"
              onValueChange={(value) => {
                setActiveTab(value);
              }}
            >
              {/* Toggle switch positioned above content - only show on AI tab */}
              {activeTab === 'ai' && (
                <div className="flex items-center justify-between py-4 px-4 border-b border-[#1A1A1A]/30">
                  {/* Toggle switch */}
                  <TabsList className="bg-[#0A0A0A] border border-[#1A1A1A]/30 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] rounded-full p-1">
                    <TabsTrigger
                      value="ai"
                      className="flex items-center gap-2 data-[state=active]:bg-[#141414] data-[state=active]:text-white data-[state=active]:shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] rounded-full px-4 py-2 transition-all duration-300"
                    >
                      <Sparkles className="h-4 w-4" />
                      Build with AI
                    </TabsTrigger>
                    <TabsTrigger
                      value="canvas"
                      className="flex items-center gap-2 data-[state=active]:bg-[#141414] data-[state=active]:text-white data-[state=active]:shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] rounded-full px-4 py-2 transition-all duration-300"
                    >
                      <Settings className="h-4 w-4" />
                      Design Canvas
                    </TabsTrigger>
                  </TabsList>
                  {/* Action buttons */}
                  <div className="flex gap-2">
                    <button
                      onClick={clearBuilder}
                      className="flex items-center gap-2 px-2 py-1 text-xs font-medium text-white/70 hover:text-white/90 transition-colors duration-200"
                    >
                      <Trash2 className="h-3.5 w-3.5" />
                      Clear
                    </button>
                    <button
                      onClick={handleRun}
                      disabled={isLoading || isSaving}
                      className="flex items-center gap-2 px-3 py-2 text-xs font-medium bg-[#141414]/60 border border-[#303035]/50 rounded-md hover:bg-[#1A1A1A]/80 transition-all duration-200 text-white/70 hover:text-white/90 disabled:opacity-50 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] hover:shadow-[inset_0_2px_4px_0_rgba(0,0,0,0.3)]"
                    >
                      <Play className="h-3.5 w-3.5" />
                      Test
                    </button>
                    <button
                      onClick={handleSave}
                      disabled={isLoading || isSaving}
                      className="flex items-center gap-2 px-3 py-2 text-xs font-medium bg-[#141414]/60 border border-[#303035]/50 rounded-md hover:bg-[#1A1A1A]/80 transition-all duration-200 text-white/70 hover:text-white/90 disabled:opacity-50 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] hover:shadow-[inset_0_2px_4px_0_rgba(0,0,0,0.3)]"
                    >
                      <Save className="h-3.5 w-3.5" />
                      {isSaving ? 'Saving...' : 'Save'}
                    </button>
                  </div>
                </div>
              )}

              <TabsContent value="ai" className="flex-1 p-0 m-0 h-full animate-fade-in relative">
                <ResizablePanelGroup direction="horizontal" className="h-full">
                  <ResizablePanel defaultSize={40} minSize={30} maxSize={50} className="h-full">
                     <AIAgentBuilder
                       onAgentGenerated={handleAIAgentGenerated}
                       userId={user?.id || ''}
                     />
                  </ResizablePanel>
                  <ResizableHandle withHandle className="bg-[#1A1A1A]/30 hover:bg-[#1A1A1A]/50 w-2" />
                  <ResizablePanel defaultSize={60} className="h-full relative">
                    <BuildCanvas
                      blocks={blocks}
                      connections={connections}
                      entryBlockId={entryBlockId}
                      onBlockUpdate={updateBlock}
                      onBlockRemove={removeBlock}
                      onConnectionAdd={addConnection}
                      onConnectionRemove={removeConnection}
                      onSetEntryBlock={setEntryBlock}
                      addBlock={addBlock}
                      errorDetails={errorDetails}
                      disconnectedBlocks={disconnectedBlocks}
                    />
                  </ResizablePanel>
                </ResizablePanelGroup>
              </TabsContent>

              <TabsContent value="canvas" className="flex-1 p-0 m-0 animate-fade-in relative">
                {/* Action buttons positioned at top-right on plain background */}
                <div className="absolute top-4 right-4 z-10 flex gap-2">
                  <button
                    onClick={clearBuilder}
                    className="flex items-center gap-2 px-2 py-1 text-xs font-medium text-white/70 hover:text-white/90 transition-colors duration-200"
                  >
                    <Trash2 className="h-3.5 w-3.5" />
                    Clear
                  </button>
                  <button
                    onClick={handleRun}
                    disabled={isLoading || isSaving}
                    className="flex items-center gap-2 px-3 py-2 text-xs font-medium bg-[#141414]/60 border border-[#303035]/50 rounded-md hover:bg-[#1A1A1A]/80 transition-all duration-200 text-white/70 hover:text-white/90 disabled:opacity-50 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] hover:shadow-[inset_0_2px_4px_0_rgba(0,0,0,0.3)]"
                  >
                    <Play className="h-3.5 w-3.5" />
                    Test
                  </button>
                  <button
                    onClick={handleSave}
                    disabled={isLoading || isSaving}
                    className="flex items-center gap-2 px-3 py-2 text-xs font-medium bg-[#141414]/60 border border-[#303035]/50 rounded-md hover:bg-[#1A1A1A]/80 transition-all duration-200 text-white/70 hover:text-white/90 disabled:opacity-50 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] hover:shadow-[inset_0_2px_4px_0_rgba(0,0,0,0.3)]"
                  >
                    <Save className="h-3.5 w-3.5" />
                    {isSaving ? 'Saving...' : 'Save'}
                  </button>
                </div>

                {/* Canvas with top padding to avoid button overlap */}
                <div className="pt-16 h-full">
                  <BuildCanvas
                    blocks={blocks}
                    connections={connections}
                    entryBlockId={entryBlockId}
                    onBlockUpdate={updateBlock}
                    onBlockRemove={removeBlock}
                    onConnectionAdd={addConnection}
                    onConnectionRemove={removeConnection}
                    onSetEntryBlock={setEntryBlock}
                    addBlock={addBlock}
                    errorDetails={errorDetails}
                    disconnectedBlocks={disconnectedBlocks}
                  />
                </div>
              </TabsContent>
            </Tabs>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>

      {/* Run Dialog */}
      <AgentRunDialog
        open={isRunDialogOpen}
        onOpenChange={setIsRunDialogOpen}
        agentId={id}
        agentConfig={!id ? getAgentConfiguration().configuration : undefined}
      />

      {/* Global Styles */}
      <style>
        {`
          .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
          }
          .custom-scrollbar::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 3px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.2);
          }

          /* Tab switching animations */
          @keyframes fadeIn {
            from {
              opacity: 0;
              transform: translateY(8px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }

          .animate-fade-in {
            animation: fadeIn 400ms cubic-bezier(0.22, 1, 0.36, 1) forwards;
          }

          /* Focus styles for inputs */
          input:focus-visible,
          textarea:focus-visible {
            outline: none !important;
            border-color: #303035 !important;
            box-shadow: none !important;
            ring: 0 !important;
          }
          input:focus,
          textarea:focus {
            outline: none !important;
            border-color: #303035 !important;
            box-shadow: none !important;
            ring: 0 !important;
          }
        `}
      </style>
    </div>
  );
};

export default AgentBuilder;
