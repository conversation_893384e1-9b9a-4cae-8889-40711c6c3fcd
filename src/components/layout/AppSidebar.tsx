import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import {
  House,
  TrendingUp,
  <PERSON><PERSON>hart,
  Hammer,
  MessagesSquare,
  Zap,
  Activity,
  Settings,
  User,
  Crown,
  Info,
  FileText,
  Shield,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import AuthButton from '@/components/auth/AuthButton';

interface SidebarItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path: string;
  description?: string;
}

interface AppSidebarProps {
  onMinimizedChange?: (isMinimized: boolean) => void;
}

const AppSidebar: React.FC<AppSidebarProps> = ({ onMinimizedChange }) => {
  const navigate = useNavigate();
  const location = useLocation();

  // Sidebar minimizer state with persistence
  const [isMinimized, setIsMinimized] = useState(() => {
    const saved = localStorage.getItem('sidebar-minimized');
    return saved ? JSON.parse(saved) : false;
  });

  // Persist minimizer state and notify parent
  useEffect(() => {
    localStorage.setItem('sidebar-minimized', JSON.stringify(isMinimized));
    onMinimizedChange?.(isMinimized);
  }, [isMinimized, onMinimizedChange]);

  const toggleMinimized = () => {
    setIsMinimized(!isMinimized);
  };

  // Main navigation items
  const mainNavItems: SidebarItem[] = [
    {
      id: 'home',
      label: 'Home',
      icon: <House className="w-4 h-4" />,
      path: '/home'
    },
    {
      id: 'stock-scanner',
      label: 'Stock Scanner',
      icon: <TrendingUp className="w-4 h-4" />,
      path: '/agent-scanner'
    },
    {
      id: 'portfolio-builder',
      label: 'Portfolio Builder',
      icon: <PieChart className="w-4 h-4" />,
      path: '/portfolio-builder'
    },
    {
      id: 'agent-builder',
      label: 'Agent Builder',
      icon: <Hammer className="w-4 h-4" />,
      path: '/agent-builder'
    },
    {
      id: 'chat',
      label: 'Chat Interface',
      icon: <MessagesSquare className="w-4 h-4" />,
      path: '/chat'
    }
  ];

  // Secondary navigation items
  const secondaryNavItems: SidebarItem[] = [
    {
      id: 'agents',
      label: 'Trading Agents',
      icon: <Zap className="w-4 h-4" />,
      path: '/agents'
    },
    {
      id: 'agent-backtesting',
      label: 'Backtesting',
      icon: <Activity className="w-4 h-4" />,
      path: '/agent-backtesting'
    }
  ];

  // Settings navigation items
  const settingsNavItems: SidebarItem[] = [
    {
      id: 'profile',
      label: 'Profile',
      icon: <User className="w-4 h-4" />,
      path: '/settings'
    },
    {
      id: 'subscription',
      label: 'Subscription',
      icon: <Crown className="w-4 h-4" />,
      path: '/subscription/manage'
    },
    {
      id: 'about',
      label: 'About',
      icon: <Info className="w-4 h-4" />,
      path: '/about'
    },
    {
      id: 'terms',
      label: 'Terms of Service',
      icon: <FileText className="w-4 h-4" />,
      path: '/terms'
    },
    {
      id: 'privacy',
      label: 'Privacy Policy',
      icon: <Shield className="w-4 h-4" />,
      path: '/privacy'
    }
  ];

  const isActiveRoute = (path: string) => {
    if (path === '/home') {
      return location.pathname === '/' || location.pathname === '/home';
    }
    if (path === '/chat') {
      return location.pathname === '/chat' || location.pathname.startsWith('/chat/');
    }
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  return (
    <div
      className={cn(
        "h-full flex flex-col relative transition-all duration-300 ease-in-out",
        isMinimized ? "w-16" : "w-52"
      )}
      style={{
        backgroundImage: `url(${import.meta.env.VITE_SUPABASE_URL}/storage/v1/object/public/backgrounds/gradient2.png)`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      {/* Gradient overlay for depth and contrast */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/[0.15] via-black/[0.05] to-black/[0.20] pointer-events-none" />

      {/* Inner shadow for depth */}
      <div className="absolute inset-0 shadow-[inset_2px_0_4px_rgba(0,0,0,0.15)] pointer-events-none" />

      {/* Minimizer Toggle Button */}
      <button
        onClick={toggleMinimized}
        className="absolute top-4 right-3 z-20 w-6 h-6 bg-white/[0.08] hover:bg-white/[0.12] rounded-md flex items-center justify-center transition-all duration-200 hover:shadow-[inset_0_1px_2px_rgba(0,0,0,0.1)]"
      >
        {isMinimized ? (
          <ChevronRight className="w-3.5 h-3.5 text-white/70" />
        ) : (
          <ChevronLeft className="w-3.5 h-3.5 text-white/70" />
        )}
      </button>
      {/* Logo/Brand Section */}
      <div className="p-4 relative z-10">
        <div className={cn("flex items-center", isMinimized ? "justify-center" : "gap-2")}>
          <img
            src="http://thecodingkid.oyosite.com/logo_only.png"
            alt="Osis Logo"
            className="h-6 w-6 drop-shadow-sm flex-shrink-0"
          />
          {!isMinimized && (
            <span className="text-lg font-semibold text-white tracking-tight">Osis</span>
          )}
        </div>
      </div>

      {/* Main Navigation */}
      <div className="flex-1 overflow-y-auto relative z-10">
        <div className="p-4">
          <div className="space-y-2">
            {mainNavItems.map((item) => (
              <button
                key={item.id}
                onClick={() => handleNavigation(item.path)}
                className={cn(
                  "w-full flex items-center rounded-md text-left transition-all duration-200 group font-hanken-grotesk",
                  isMinimized ? "justify-center px-2 py-2" : "gap-2.5 px-2.5 py-1.5",
                  isActiveRoute(item.path)
                    ? "bg-white/[0.08] text-white shadow-[inset_0_1px_2px_rgba(0,0,0,0.1)]"
                    : "text-white/70 hover:text-white hover:bg-white/[0.04] hover:shadow-[inset_0_1px_2px_rgba(0,0,0,0.05)]"
                )}
                title={isMinimized ? item.label : undefined}
              >
                <div className={cn(
                  "flex-shrink-0 transition-colors duration-200",
                  isActiveRoute(item.path) ? "text-white" : "text-white group-hover:text-white"
                )}>
                  {item.icon}
                </div>
                {!isMinimized && (
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-xs truncate tracking-wide">
                      {item.label}
                    </div>
                  </div>
                )}
              </button>
            ))}
          </div>

          {/* Secondary Navigation */}
          {!isMinimized && (
            <div className="mt-6">
              <div className="px-2.5 mb-3">
                <span className="uppercase text-xs font-medium text-white/40 tracking-wider font-hanken-grotesk">Tools</span>
              </div>
              <div className="space-y-2">
                {secondaryNavItems.map((item) => (
                  <button
                    key={item.id}
                    onClick={() => handleNavigation(item.path)}
                    className={cn(
                      "w-full flex items-center gap-2.5 px-2.5 py-1.5 rounded-md text-left transition-all duration-200 group font-hanken-grotesk",
                      isActiveRoute(item.path)
                        ? "bg-white/[0.08] text-white shadow-[inset_0_1px_2px_rgba(0,0,0,0.1)]"
                        : "text-white/70 hover:text-white hover:bg-white/[0.04] hover:shadow-[inset_0_1px_2px_rgba(0,0,0,0.05)]"
                    )}
                  >
                    <div className={cn(
                      "flex-shrink-0 transition-colors duration-200",
                      isActiveRoute(item.path) ? "text-white" : "text-white group-hover:text-white"
                    )}>
                      {item.icon}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-xs truncate tracking-wide">
                        {item.label}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Settings Navigation */}
          {!isMinimized && (
            <div className="mt-6">
              <div className="px-2.5 mb-3">
                <span className="uppercase text-xs font-medium text-white/40 tracking-wider font-hanken-grotesk">Settings</span>
              </div>
              <div className="space-y-2">
                {settingsNavItems.map((item) => (
                  <button
                    key={item.id}
                    onClick={() => handleNavigation(item.path)}
                    className={cn(
                      "w-full flex items-center gap-2.5 px-2.5 py-1.5 rounded-md text-left transition-all duration-200 group font-hanken-grotesk",
                      isActiveRoute(item.path)
                        ? "bg-white/[0.08] text-white shadow-[inset_0_1px_2px_rgba(0,0,0,0.1)]"
                        : "text-white/70 hover:text-white hover:bg-white/[0.04] hover:shadow-[inset_0_1px_2px_rgba(0,0,0,0.05)]"
                    )}
                  >
                    <div className={cn(
                      "flex-shrink-0 transition-colors duration-200",
                      isActiveRoute(item.path) ? "text-white" : "text-white group-hover:text-white"
                    )}>
                      {item.icon}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-xs truncate tracking-wide">
                        {item.label}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Profile Section */}
      {!isMinimized && (
        <div className="p-3 relative z-10">
          <div className="text-[10px] text-white/40 font-hanken-grotesk tracking-wide">
            Osis AI Trading Platform
          </div>
        </div>
      )}
    </div>
  );
};

export default AppSidebar;
