import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import {
  Home,
  TrendingUp,
  Briefcase,
  Hammer,
  MessageCircle,
  Bot,
  Search,
  BarChart3
} from 'lucide-react';
import AuthButton from '@/components/auth/AuthButton';

interface SidebarItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path: string;
  description?: string;
}

const AppSidebar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Main navigation items
  const mainNavItems: SidebarItem[] = [
    {
      id: 'home',
      label: 'Home',
      icon: <Home className="w-4 h-4" />,
      path: '/home',
      description: 'Dashboard and overview'
    },
    {
      id: 'stock-scanner',
      label: 'Stock Scanner',
      icon: <TrendingUp className="w-4 h-4" />,
      path: '/agent-scanner',
      description: 'Discover trending stocks'
    },
    {
      id: 'portfolio-builder',
      label: 'Portfolio Builder',
      icon: <Briefcase className="w-4 h-4" />,
      path: '/portfolio-builder',
      description: 'Build your portfolio'
    },
    {
      id: 'agent-builder',
      label: 'Agent Builder',
      icon: <Hammer className="w-4 h-4" />,
      path: '/agent-builder',
      description: 'Create trading agents'
    },
    {
      id: 'chat',
      label: 'Chat Interface',
      icon: <MessageCircle className="w-4 h-4" />,
      path: '/chat',
      description: 'AI trading assistant'
    }
  ];

  // Secondary navigation items
  const secondaryNavItems: SidebarItem[] = [
    {
      id: 'agents',
      label: 'Trading Agents',
      icon: <Bot className="w-3.5 h-3.5" />,
      path: '/agents',
      description: 'Manage your agents'
    },
    {
      id: 'agent-backtesting',
      label: 'Backtesting',
      icon: <BarChart3 className="w-3.5 h-3.5" />,
      path: '/agent-backtesting',
      description: 'Test your strategies'
    }
  ];

  const isActiveRoute = (path: string) => {
    if (path === '/home') {
      return location.pathname === '/' || location.pathname === '/home';
    }
    if (path === '/chat') {
      return location.pathname === '/chat' || location.pathname.startsWith('/chat/');
    }
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  return (
    <div className="w-64 h-full bg-[#141414] flex flex-col">
      {/* Logo/Brand Section */}
      <div className="p-4">
        <div className="flex items-center gap-2">
          <img
            src="http://thecodingkid.oyosite.com/logo_only.png"
            alt="Osis Logo"
            className="h-6 w-6 drop-shadow-sm"
          />
          <span className="text-lg font-semibold text-white tracking-tight">Osis</span>
        </div>
      </div>

      {/* Main Navigation */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4">
          <div className="space-y-2">
            {mainNavItems.map((item) => (
              <button
                key={item.id}
                onClick={() => handleNavigation(item.path)}
                className={cn(
                  "w-full flex items-center gap-2.5 px-2.5 py-1.5 rounded-md text-left transition-all duration-200 group",
                  isActiveRoute(item.path)
                    ? "bg-white/[0.08] text-white shadow-[inset_0_1px_2px_rgba(0,0,0,0.1)]"
                    : "text-white/70 hover:text-white hover:bg-white/[0.04] hover:shadow-[inset_0_1px_2px_rgba(0,0,0,0.05)]"
                )}
              >
                <div className={cn(
                  "flex-shrink-0 transition-colors duration-200",
                  isActiveRoute(item.path) ? "text-white" : "text-white group-hover:text-white"
                )}>
                  {item.icon}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-xs truncate">
                    {item.label}
                  </div>
                  {item.description && (
                    <div className={cn(
                      "text-[10px] mt-0.5 truncate transition-colors duration-200",
                      isActiveRoute(item.path) ? "text-white/60" : "text-white/40 group-hover:text-white/50"
                    )}>
                      {item.description}
                    </div>
                  )}
                </div>
              </button>
            ))}
          </div>

          {/* Secondary Navigation */}
          <div className="mt-8">
            <div className="px-3 mb-3">
              <h3 className="text-xs font-medium text-white/40 uppercase tracking-wider">
                Tools
              </h3>
            </div>
            <div className="space-y-1">
              {secondaryNavItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleNavigation(item.path)}
                  className={cn(
                    "w-full flex items-center gap-2 px-2.5 py-1 rounded-md text-left transition-all duration-200 group",
                    isActiveRoute(item.path)
                      ? "bg-white/[0.06] text-white shadow-[inset_0_1px_2px_rgba(0,0,0,0.08)]"
                      : "text-white/60 hover:text-white hover:bg-white/[0.03] hover:shadow-[inset_0_1px_2px_rgba(0,0,0,0.04)]"
                  )}
                >
                  <div className={cn(
                    "flex-shrink-0 transition-colors duration-200",
                    isActiveRoute(item.path) ? "text-white" : "text-white group-hover:text-white"
                  )}>
                    {item.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-[11px] truncate">
                      {item.label}
                    </div>
                    {item.description && (
                      <div className={cn(
                        "text-[9px] mt-0.5 truncate transition-colors duration-200",
                        isActiveRoute(item.path) ? "text-white/50" : "text-white/35 group-hover:text-white/45"
                      )}>
                        {item.description}
                      </div>
                    )}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Profile Section */}
      <div className="p-3">
        <div className="flex items-center justify-between">
          <div className="text-[10px] text-white/40">
            Osis AI Trading Platform
          </div>
          <AuthButton />
        </div>
      </div>
    </div>
  );
};

export default AppSidebar;
