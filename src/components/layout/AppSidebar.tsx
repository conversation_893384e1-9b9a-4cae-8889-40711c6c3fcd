import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import {
  Home,
  TrendingUp,
  Briefcase,
  Hammer,
  MessageCircle,
  Bot,
  Search,
  BarChart3
} from 'lucide-react';

interface SidebarItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path: string;
  description?: string;
}

const AppSidebar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Main navigation items
  const mainNavItems: SidebarItem[] = [
    {
      id: 'home',
      label: 'Home',
      icon: <Home className="w-5 h-5" />,
      path: '/home',
      description: 'Dashboard and overview'
    },
    {
      id: 'stock-scanner',
      label: 'Stock Scanner',
      icon: <TrendingUp className="w-5 h-5" />,
      path: '/agent-scanner',
      description: 'Discover trending stocks'
    },
    {
      id: 'portfolio-builder',
      label: 'Portfolio Builder',
      icon: <Briefcase className="w-5 h-5" />,
      path: '/portfolio-builder',
      description: 'Build your portfolio'
    },
    {
      id: 'agent-builder',
      label: 'Agent Builder',
      icon: <Hammer className="w-5 h-5" />,
      path: '/agent-builder',
      description: 'Create trading agents'
    },
    {
      id: 'chat',
      label: 'Chat Interface',
      icon: <MessageCircle className="w-5 h-5" />,
      path: '/chat',
      description: 'AI trading assistant'
    }
  ];

  // Secondary navigation items
  const secondaryNavItems: SidebarItem[] = [
    {
      id: 'agents',
      label: 'Trading Agents',
      icon: <Bot className="w-4 h-4" />,
      path: '/agents',
      description: 'Manage your agents'
    },
    {
      id: 'agent-backtesting',
      label: 'Backtesting',
      icon: <BarChart3 className="w-4 h-4" />,
      path: '/agent-backtesting',
      description: 'Test your strategies'
    }
  ];

  const isActiveRoute = (path: string) => {
    if (path === '/home') {
      return location.pathname === '/' || location.pathname === '/home';
    }
    if (path === '/chat') {
      return location.pathname === '/chat' || location.pathname.startsWith('/chat/');
    }
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const handleNavigation = (path: string) => {
    if (path === '/chat') {
      // For chat, navigate to root to create new chat
      navigate('/');
    } else {
      navigate(path);
    }
  };

  return (
    <div className="w-64 h-full bg-[#141414] border-r border-white/[0.08] flex flex-col">
      {/* Logo/Brand Section */}
      <div className="p-6 border-b border-white/[0.06]">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-emerald-400 to-blue-400 flex items-center justify-center">
            <span className="text-white font-bold text-sm">O</span>
          </div>
          <span className="text-xl font-semibold text-white">Osis</span>
        </div>
      </div>

      {/* Main Navigation */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4">
          <div className="space-y-2">
            {mainNavItems.map((item) => (
              <button
                key={item.id}
                onClick={() => handleNavigation(item.path)}
                className={cn(
                  "w-full flex items-center gap-3 px-3 py-3 rounded-lg text-left transition-all duration-200 group",
                  isActiveRoute(item.path)
                    ? "bg-white/[0.08] text-white border border-white/[0.12]"
                    : "text-white/70 hover:text-white hover:bg-white/[0.04] border border-transparent"
                )}
              >
                <div className={cn(
                  "flex-shrink-0 transition-colors duration-200",
                  isActiveRoute(item.path) ? "text-white" : "text-white/60 group-hover:text-white/80"
                )}>
                  {item.icon}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm truncate">
                    {item.label}
                  </div>
                  {item.description && (
                    <div className={cn(
                      "text-xs mt-0.5 truncate transition-colors duration-200",
                      isActiveRoute(item.path) ? "text-white/60" : "text-white/40 group-hover:text-white/50"
                    )}>
                      {item.description}
                    </div>
                  )}
                </div>
              </button>
            ))}
          </div>

          {/* Secondary Navigation */}
          <div className="mt-8">
            <div className="px-3 mb-3">
              <h3 className="text-xs font-medium text-white/40 uppercase tracking-wider">
                Tools
              </h3>
            </div>
            <div className="space-y-1">
              {secondaryNavItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleNavigation(item.path)}
                  className={cn(
                    "w-full flex items-center gap-3 px-3 py-2 rounded-md text-left transition-all duration-200 group",
                    isActiveRoute(item.path)
                      ? "bg-white/[0.06] text-white"
                      : "text-white/60 hover:text-white hover:bg-white/[0.03]"
                  )}
                >
                  <div className={cn(
                    "flex-shrink-0 transition-colors duration-200",
                    isActiveRoute(item.path) ? "text-white" : "text-white/50 group-hover:text-white/70"
                  )}>
                    {item.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-sm truncate">
                      {item.label}
                    </div>
                    {item.description && (
                      <div className={cn(
                        "text-xs mt-0.5 truncate transition-colors duration-200",
                        isActiveRoute(item.path) ? "text-white/50" : "text-white/35 group-hover:text-white/45"
                      )}>
                        {item.description}
                      </div>
                    )}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Footer Section */}
      <div className="p-4 border-t border-white/[0.06]">
        <div className="text-xs text-white/40 text-center">
          Osis AI Trading Platform
        </div>
      </div>
    </div>
  );
};

export default AppSidebar;
