import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import {
  House,
  TrendingUp,
  PieChart,
  Hammer,
  MessagesSquare,
  Zap,
  Activity,
  Settings,
  User,
  Crown,
  HelpCircle,
  FileText,
  Shield
} from 'lucide-react';
import AuthButton from '@/components/auth/AuthButton';

interface SidebarItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path: string;
  description?: string;
}

const AppSidebar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Main navigation items
  const mainNavItems: SidebarItem[] = [
    {
      id: 'home',
      label: 'Home',
      icon: <House className="w-4 h-4" fill="currentColor" />,
      path: '/home'
    },
    {
      id: 'stock-scanner',
      label: 'Stock Scanner',
      icon: <TrendingUp className="w-4 h-4" fill="currentColor" />,
      path: '/agent-scanner'
    },
    {
      id: 'portfolio-builder',
      label: 'Portfolio Builder',
      icon: <PieChart className="w-4 h-4" fill="currentColor" />,
      path: '/portfolio-builder'
    },
    {
      id: 'agent-builder',
      label: 'Agent Builder',
      icon: <Hammer className="w-4 h-4" fill="currentColor" />,
      path: '/agent-builder'
    },
    {
      id: 'chat',
      label: 'Chat Interface',
      icon: <MessagesSquare className="w-4 h-4" fill="currentColor" />,
      path: '/chat'
    }
  ];

  // Secondary navigation items
  const secondaryNavItems: SidebarItem[] = [
    {
      id: 'agents',
      label: 'Trading Agents',
      icon: <Zap className="w-3.5 h-3.5" fill="currentColor" />,
      path: '/agents'
    },
    {
      id: 'agent-backtesting',
      label: 'Backtesting',
      icon: <Activity className="w-3.5 h-3.5" fill="currentColor" />,
      path: '/agent-backtesting'
    }
  ];

  // Settings navigation items
  const settingsNavItems: SidebarItem[] = [
    {
      id: 'profile',
      label: 'Profile',
      icon: <User className="w-3.5 h-3.5" fill="currentColor" />,
      path: '/settings'
    },
    {
      id: 'subscription',
      label: 'Subscription',
      icon: <Crown className="w-3.5 h-3.5" fill="currentColor" />,
      path: '/subscription/manage'
    },
    {
      id: 'about',
      label: 'About',
      icon: <HelpCircle className="w-3.5 h-3.5" fill="currentColor" />,
      path: '/about'
    },
    {
      id: 'terms',
      label: 'Terms of Service',
      icon: <FileText className="w-3.5 h-3.5" fill="currentColor" />,
      path: '/terms'
    },
    {
      id: 'privacy',
      label: 'Privacy Policy',
      icon: <Shield className="w-3.5 h-3.5" fill="currentColor" />,
      path: '/privacy'
    }
  ];

  const isActiveRoute = (path: string) => {
    if (path === '/home') {
      return location.pathname === '/' || location.pathname === '/home';
    }
    if (path === '/chat') {
      return location.pathname === '/chat' || location.pathname.startsWith('/chat/');
    }
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  return (
    <div className="w-64 h-full bg-[#141414] flex flex-col relative">
      {/* Subtle gradient overlay for depth */}
      <div className="absolute inset-0 bg-gradient-to-b from-white/[0.02] via-transparent to-black/[0.03] pointer-events-none" />

      {/* Inner shadow for depth */}
      <div className="absolute inset-0 shadow-[inset_2px_0_4px_rgba(0,0,0,0.1)] pointer-events-none" />
      {/* Logo/Brand Section */}
      <div className="p-4 relative z-10">
        <div className="flex items-center gap-2">
          <img
            src="http://thecodingkid.oyosite.com/logo_only.png"
            alt="Osis Logo"
            className="h-6 w-6 drop-shadow-sm"
          />
          <span className="text-lg font-semibold text-white tracking-tight">Osis</span>
        </div>
      </div>

      {/* Main Navigation */}
      <div className="flex-1 overflow-y-auto relative z-10">
        <div className="p-4">
          <div className="space-y-2">
            {mainNavItems.map((item) => (
              <button
                key={item.id}
                onClick={() => handleNavigation(item.path)}
                className={cn(
                  "w-full flex items-center gap-2.5 px-2.5 py-1.5 rounded-md text-left transition-all duration-200 group font-hanken-grotesk",
                  isActiveRoute(item.path)
                    ? "bg-white/[0.08] text-white shadow-[inset_0_1px_2px_rgba(0,0,0,0.1)]"
                    : "text-white/70 hover:text-white hover:bg-white/[0.04] hover:shadow-[inset_0_1px_2px_rgba(0,0,0,0.05)]"
                )}
              >
                <div className={cn(
                  "flex-shrink-0 transition-colors duration-200",
                  isActiveRoute(item.path) ? "text-white" : "text-white group-hover:text-white"
                )}>
                  {item.icon}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-xs truncate tracking-wide">
                    {item.label}
                  </div>
                </div>
              </button>
            ))}
          </div>

          {/* Secondary Navigation */}
          <div className="mt-6">
            <div className="px-2.5 mb-3">
              <span className="uppercase text-sm font-medium text-white/40 tracking-wider font-hanken-grotesk">Tools</span>
            </div>
            <div className="space-y-1">
              {secondaryNavItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleNavigation(item.path)}
                  className={cn(
                    "w-full flex items-center gap-2 px-2.5 py-1 rounded-md text-left transition-all duration-200 group font-hanken-grotesk",
                    isActiveRoute(item.path)
                      ? "bg-white/[0.06] text-white shadow-[inset_0_1px_2px_rgba(0,0,0,0.08)]"
                      : "text-white/60 hover:text-white hover:bg-white/[0.03] hover:shadow-[inset_0_1px_2px_rgba(0,0,0,0.04)]"
                  )}
                >
                  <div className={cn(
                    "flex-shrink-0 transition-colors duration-200",
                    isActiveRoute(item.path) ? "text-white" : "text-white group-hover:text-white"
                  )}>
                    {item.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-[11px] truncate tracking-wide">
                      {item.label}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Settings Navigation */}
          <div className="mt-6">
            <div className="px-2.5 mb-3">
              <span className="uppercase text-sm font-medium text-white/40 tracking-wider font-hanken-grotesk">Settings</span>
            </div>
            <div className="space-y-1">
              {settingsNavItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleNavigation(item.path)}
                  className={cn(
                    "w-full flex items-center gap-2 px-2.5 py-1 rounded-md text-left transition-all duration-200 group font-hanken-grotesk",
                    isActiveRoute(item.path)
                      ? "bg-white/[0.06] text-white shadow-[inset_0_1px_2px_rgba(0,0,0,0.08)]"
                      : "text-white/60 hover:text-white hover:bg-white/[0.03] hover:shadow-[inset_0_1px_2px_rgba(0,0,0,0.04)]"
                  )}
                >
                  <div className={cn(
                    "flex-shrink-0 transition-colors duration-200",
                    isActiveRoute(item.path) ? "text-white" : "text-white group-hover:text-white"
                  )}>
                    {item.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-[11px] truncate tracking-wide">
                      {item.label}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Profile Section */}
      <div className="p-3 relative z-10">
        <div className="text-[10px] text-white/40 font-hanken-grotesk tracking-wide">
          Osis AI Trading Platform
        </div>
      </div>
    </div>
  );
};

export default AppSidebar;
