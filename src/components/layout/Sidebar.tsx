import { useState } from "react";
import { Search, PenSquare, ChevronDown, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger, DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import { useNavigate } from 'react-router-dom';

interface SidebarProps {
  isCollapsed: boolean;
  setIsCollapsed: (collapsed: boolean) => void;
}

const Sidebar = ({
  isCollapsed,
  setIsCollapsed
}: SidebarProps) => {
  const [model, setModel] = useState("Osis");
  const navigate = useNavigate();

  const handleSearchClick = () => {
  };

  const handleEditClick = () => {
  };

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <>
      <div className={cn(
        "fixed left-0 top-0 z-40 flex h-screen flex-col bg-[#141414] transition-all duration-300",
        isCollapsed ? "w-[60px]" : "w-[240px]"
      )}>
        <div className="h-12 flex items-center justify-between px-2">
          <Button 
            variant="ghost" 
            size="icon" 
            className="w-9 h-9 text-muted-foreground hover:text-foreground"
            onClick={toggleSidebar}
          >
            {isCollapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronRight className="w-4 h-4 rotate-180" />}
          </Button>
          {!isCollapsed && (
            <div className="flex items-center gap-2">
              <Button 
                variant="ghost" 
                size="icon" 
                className="w-9 h-9 text-muted-foreground hover:text-foreground"
                onClick={handleSearchClick}
              >
                <Search className="w-4 h-4" />
              </Button>
              <Button 
                variant="ghost" 
                size="icon" 
                className="w-9 h-9 text-muted-foreground hover:text-foreground"
                onClick={handleEditClick}
              >
                <PenSquare className="w-4 h-4" />
              </Button>
            </div>
          )}
          {isCollapsed && (
            <Button 
              variant="ghost" 
              size="icon" 
              className="w-9 h-9 text-muted-foreground hover:text-foreground"
              onClick={handleEditClick}
            >
              <PenSquare className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="ghost" 
            className={cn(
              "fixed top-0 z-50 gap-2 px-3 h-12 rounded-none transition-all duration-300 w-[240px] justify-start bg-[#141414]",
              isCollapsed ? "left-[60px]" : "left-[240px]"
            )}
          >
            Osis
            <ChevronDown className="w-4 h-4 opacity-50 ml-auto" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-[260px]">
          <div className="flex flex-col gap-4 p-4">
            <div className="flex justify-between items-start">
              <div className="flex flex-col">
                <span className="font-medium">Osis Plus</span>
                <span className="text-sm text-muted-foreground">Our smartest model & more</span>
              </div>
              <Button size="sm" variant="secondary">Upgrade</Button>
            </div>
            <div className="flex justify-between items-start">
              <div className="flex flex-col">
                <span className="font-medium">Osis</span>
                <span className="text-sm text-muted-foreground">Great for everyday tasks</span>
              </div>
            </div>
            <DropdownMenuItem onClick={() => navigate('/agent-builder')}>
              Agent Builder
            </DropdownMenuItem>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
};

export default Sidebar;
