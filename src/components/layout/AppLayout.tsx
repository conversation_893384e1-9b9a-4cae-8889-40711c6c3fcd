import React from 'react';
import { useLocation } from 'react-router-dom';
import AppSidebar from './AppSidebar';
import Header from './Header';
import { cn } from '@/lib/utils';

interface AppLayoutProps {
  children: React.ReactNode;
  loading?: boolean;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children, loading }) => {
  const location = useLocation();

  // Check if we're on a subscription page or other pages that shouldn't have the sidebar
  const isSubscriptionPage = location.pathname.includes('/subscription');
  const isAuthPage = location.pathname.includes('/auth') || location.pathname.includes('/login');
  const isTermsOrPrivacy = ['/terms', '/privacy'].includes(location.pathname);

  // Pages that should not use the new layout
  const shouldUseOldLayout = isSubscriptionPage || isAuthPage || isTermsOrPrivacy;

  if (shouldUseOldLayout) {
    // Use the old layout for these pages
    return (
      <div className="min-h-screen bg-[#0A0A0A]">
        <Header onNewChat={() => window.location.href = "/"} />
        <main className="pt-14">
          {children}
        </main>
      </div>
    );
  }

  const handleNewChat = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    // Navigate directly to the root URL instead of refreshing
    window.location.href = "/";
  };

  return (
    <div className="h-screen bg-[#141414] overflow-hidden flex">
      {/* Sidebar - Fixed behind main content */}
      <div className="fixed inset-y-0 left-0 z-10">
        <AppSidebar />
      </div>

      {/* Main Content Area - Floating on top */}
      <div className="flex-1 ml-64 relative z-20">
        {/* Floating Main Content Container */}
        <div className="h-full bg-[#0A0A0A] rounded-l-2xl shadow-2xl border-l border-t border-b border-white/[0.08] overflow-hidden flex flex-col">
          {/* Header */}
          <div className="flex-shrink-0">
            <Header onNewChat={handleNewChat} />
          </div>

          {/* Main Content */}
          <main className="flex-1 overflow-auto">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white/30"></div>
              </div>
            ) : (
              children
            )}
          </main>
        </div>
      </div>

      {/* Custom Styles for Enhanced Visual Effects */}
      <style>{`
        /* Enhanced shadow for floating effect */
        .shadow-2xl {
          box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.8),
            0 0 0 1px rgba(255, 255, 255, 0.05),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        /* Smooth transitions for all interactive elements */
        * {
          transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
          transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
          transition-duration: 200ms;
        }

        /* Custom scrollbar for main content */
        main::-webkit-scrollbar {
          width: 6px;
        }

        main::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.02);
        }

        main::-webkit-scrollbar-thumb {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 3px;
        }

        main::-webkit-scrollbar-thumb:hover {
          background: rgba(255, 255, 255, 0.15);
        }

        /* Ensure proper layering */
        .z-10 {
          z-index: 10;
        }

        .z-20 {
          z-index: 20;
        }

        /* Subtle gradient overlay for depth */
        .bg-\\[\\#141414\\]::before {
          content: '';
          position: absolute;
          inset: 0;
          background: radial-gradient(circle at 50% 0%, rgba(255, 255, 255, 0.02) 0%, transparent 50%);
          pointer-events: none;
        }
      `}</style>
    </div>
  );
};

export default AppLayout;
