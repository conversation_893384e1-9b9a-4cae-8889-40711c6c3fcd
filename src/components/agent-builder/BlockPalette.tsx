import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { BarChart2, TrendingUp, TrendingDown, DollarSign, Calculator, GitBranch, Target, Zap } from 'lucide-react';
import { BlockType } from '@/hooks/useAgentBuilder';

interface BlockPaletteProps {
  onBlockDrop: (type: string, position: { x: number; y: number }) => void;
}

interface BlockItemProps {
  type: BlockType;
  title: string;
  icon: React.ReactNode;
  description: string;
  onBlockDrop: (type: string, position: { x: number; y: number }) => void;
  properties?: Record<string, any>;
}

const BlockItem: React.FC<BlockItemProps> = ({ type, title, icon, description, onBlockDrop, properties }) => {
  const handleDragStart = (e: React.DragEvent) => {
    // Store both the type and any additional properties
    const dragData = {
      type,
      properties: properties || {}
    };

    // Ensure position is not included in the properties to avoid issues
    if (dragData.properties.position) {
      delete dragData.properties.position;
    }

    e.dataTransfer.setData('application/reactflow', JSON.stringify(dragData));
    e.dataTransfer.effectAllowed = 'move';
  };

  return (
    <div
      className="group mb-3 cursor-grab select-none transition-all duration-200 hover:scale-[1.02] active:scale-[0.98]"
      draggable
      onDragStart={handleDragStart}
    >
      <div className="relative overflow-hidden rounded-xl border border-white/5 bg-gradient-to-br from-[#1A1A1A]/80 to-[#141414]/60 backdrop-blur-sm transition-all duration-200 hover:border-white/10 hover:shadow-lg hover:shadow-black/20">
        {/* Subtle gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/[0.02] to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200" />

        <div className="relative p-4 flex items-center gap-3">
          {/* Icon container with modern styling */}
          <div className="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-gradient-to-br from-white/10 to-white/5 border border-white/10 group-hover:border-white/20 transition-all duration-200">
            <div className="text-white/80 group-hover:text-white transition-colors duration-200">
              {icon}
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-semibold text-white/90 group-hover:text-white transition-colors duration-200 truncate">
              {title}
            </h3>
            <p className="text-xs text-white/50 group-hover:text-white/60 transition-colors duration-200 line-clamp-2 mt-0.5">
              {description}
            </p>
          </div>

          {/* Drag indicator */}
          <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <div className="w-1 h-6 bg-gradient-to-b from-white/20 to-white/10 rounded-full" />
          </div>
        </div>

        {/* Bottom accent line */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
      </div>
    </div>
  );
};

const BlockPalette: React.FC<BlockPaletteProps> = ({ onBlockDrop }) => {
  return (
    <div className="space-y-4">
      <Accordion type="multiple" defaultValue={['data', 'logic', 'actions', 'output']} className="space-y-3">
        <AccordionItem value="data" className="border-none">
          <AccordionTrigger className="py-3 px-0 text-sm font-semibold text-white/90 hover:text-white transition-colors duration-200 hover:no-underline border-b border-white/5 hover:border-white/10">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-blue-400/60" />
              Data Sources
            </div>
          </AccordionTrigger>
          <AccordionContent className="pt-3 pb-1">
          <BlockItem
            type={BlockType.INDICATOR}
            title="Technical Indicator"
            icon={<BarChart2 className="h-4 w-4" />}
            description="RSI, MACD, SMA, EMA, Bollinger Bands, Support/Resistance, Candle Patterns, etc."
            onBlockDrop={onBlockDrop}
            properties={{ indicator: 'rsi', parameters: { period: 14 } }}
          />
          <BlockItem
            type={BlockType.PRICE}
            title="Price Data"
            icon={<DollarSign className="h-4 w-4" />}
            description="Open, High, Low, Close, Volume"
            onBlockDrop={onBlockDrop}
            properties={{ dataPoint: 'close', lookback: 0 }}
          />
          <BlockItem
            type={BlockType.FUNDAMENTAL}
            title="Fundamental Data"
            icon={<TrendingUp className="h-4 w-4" />}
            description="P/E, P/B, ROE, Revenue Growth, etc."
            onBlockDrop={onBlockDrop}
            properties={{ metric: 'return_on_equity', statement: 'calculated', period: 'quarterly' }}
          />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="logic" className="border-none">
          <AccordionTrigger className="py-3 px-0 text-sm font-semibold text-white/90 hover:text-white transition-colors duration-200 hover:no-underline border-b border-white/5 hover:border-white/10">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-purple-400/60" />
              Logic & Processing
            </div>
          </AccordionTrigger>
          <AccordionContent className="pt-3 pb-1">
          <BlockItem
            type={BlockType.CONDITION}
            title="Condition"
            icon={<GitBranch className="h-4 w-4" />}
            description=">, <, ==, >=, <=, !=, AND, OR, etc."
            onBlockDrop={onBlockDrop}
            properties={{ operator: '>', compareValue: 0 }}
          />
          <BlockItem
            type={BlockType.OPERATOR}
            title="Math Operator"
            icon={<Calculator className="h-4 w-4" />}
            description="Add, Subtract, Multiply, Divide, Average, etc."
            onBlockDrop={onBlockDrop}
            properties={{ operation: 'add' }}
          />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="actions" className="border-none">
          <AccordionTrigger className="py-3 px-0 text-sm font-semibold text-white/90 hover:text-white transition-colors duration-200 hover:no-underline border-b border-white/5 hover:border-white/10">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-green-400/60" />
              Actions
            </div>
          </AccordionTrigger>
          <AccordionContent className="pt-3 pb-1">
          <BlockItem
            type={BlockType.PERCENTAGE_UP}
            title="Percentage Up"
            icon={<TrendingUp className="h-4 w-4" />}
            description="Triggers when price moves up by specified percentage"
            onBlockDrop={onBlockDrop}
            properties={{ percentage: 5.0 }}
          />
          <BlockItem
            type={BlockType.PERCENTAGE_DOWN}
            title="Percentage Down"
            icon={<TrendingDown className="h-4 w-4" />}
            description="Triggers when price moves down by specified percentage"
            onBlockDrop={onBlockDrop}
            properties={{ percentage: 10.0 }}
          />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="output" className="border-none">
          <AccordionTrigger className="py-3 px-0 text-sm font-semibold text-white/90 hover:text-white transition-colors duration-200 hover:no-underline border-b border-white/5 hover:border-white/10">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-orange-400/60" />
              Output Signals
            </div>
          </AccordionTrigger>
          <AccordionContent className="pt-3 pb-1">
          <BlockItem
            type={BlockType.TRIGGER}
            title="Signal"
            icon={<Target className="h-4 w-4" />}
            description="Bullish, Bearish, or Neutral signal"
            onBlockDrop={onBlockDrop}
            properties={{ signal: 'bullish', confidence: 75 }}
          />
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default BlockPalette;
