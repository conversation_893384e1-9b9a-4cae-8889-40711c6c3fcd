import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { BarChart2, TrendingUp, TrendingDown, DollarSign, Calculator, GitBranch, Target, Zap } from 'lucide-react';
import { BlockType } from '@/hooks/useAgentBuilder';

interface BlockPaletteProps {
  onBlockDrop: (type: string, position: { x: number; y: number }) => void;
}

interface BlockItemProps {
  type: BlockType;
  title: string;
  icon: React.ReactNode;
  description: string;
  onBlockDrop: (type: string, position: { x: number; y: number }) => void;
  properties?: Record<string, any>;
}

const BlockItem: React.FC<BlockItemProps> = ({ type, title, icon, description, onBlockDrop, properties }) => {
  const handleDragStart = (e: React.DragEvent) => {
    // Store both the type and any additional properties
    const dragData = {
      type,
      properties: properties || {}
    };

    // Ensure position is not included in the properties to avoid issues
    if (dragData.properties.position) {
      delete dragData.properties.position;
    }

    e.dataTransfer.setData('application/reactflow', JSON.stringify(dragData));
    e.dataTransfer.effectAllowed = 'move';
  };

  return (
    <Card
      className="mb-2 cursor-grab hover:bg-accent transition-colors"
      draggable
      onDragStart={handleDragStart}
    >
      <CardContent className="p-3 flex items-center gap-3">
        <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full bg-primary/10 text-primary">
          {icon}
        </div>
        <div>
          <h3 className="text-sm font-medium">{title}</h3>
          <p className="text-xs text-muted-foreground">{description}</p>
        </div>
      </CardContent>
    </Card>
  );
};

const BlockPalette: React.FC<BlockPaletteProps> = ({ onBlockDrop }) => {
  return (
    <Accordion type="multiple" defaultValue={['data', 'logic', 'actions', 'output']}>
      <AccordionItem value="data">
        <AccordionTrigger className="py-2">Data Sources</AccordionTrigger>
        <AccordionContent>
          <BlockItem
            type={BlockType.INDICATOR}
            title="Technical Indicator"
            icon={<BarChart2 className="h-4 w-4" />}
            description="RSI, MACD, SMA, EMA, Bollinger Bands, Support/Resistance, Candle Patterns, etc."
            onBlockDrop={onBlockDrop}
            properties={{ indicator: 'rsi', parameters: { period: 14 } }}
          />
          <BlockItem
            type={BlockType.PRICE}
            title="Price Data"
            icon={<DollarSign className="h-4 w-4" />}
            description="Open, High, Low, Close, Volume"
            onBlockDrop={onBlockDrop}
            properties={{ dataPoint: 'close', lookback: 0 }}
          />
          <BlockItem
            type={BlockType.FUNDAMENTAL}
            title="Fundamental Data"
            icon={<TrendingUp className="h-4 w-4" />}
            description="P/E, P/B, ROE, Revenue Growth, etc."
            onBlockDrop={onBlockDrop}
            properties={{ metric: 'return_on_equity', statement: 'calculated', period: 'quarterly' }}
          />
        </AccordionContent>
      </AccordionItem>

      <AccordionItem value="logic">
        <AccordionTrigger className="py-2">Logic & Processing</AccordionTrigger>
        <AccordionContent>
          <BlockItem
            type={BlockType.CONDITION}
            title="Condition"
            icon={<GitBranch className="h-4 w-4" />}
            description=">, <, ==, >=, <=, !=, AND, OR, etc."
            onBlockDrop={onBlockDrop}
            properties={{ operator: '>', compareValue: 0 }}
          />
          <BlockItem
            type={BlockType.OPERATOR}
            title="Math Operator"
            icon={<Calculator className="h-4 w-4" />}
            description="Add, Subtract, Multiply, Divide, Average, etc."
            onBlockDrop={onBlockDrop}
            properties={{ operation: 'add' }}
          />
        </AccordionContent>
      </AccordionItem>

      <AccordionItem value="actions">
        <AccordionTrigger className="py-2">Actions</AccordionTrigger>
        <AccordionContent>
          <BlockItem
            type={BlockType.PERCENTAGE_UP}
            title="Percentage Up"
            icon={<TrendingUp className="h-4 w-4" />}
            description="Triggers when price moves up by specified percentage"
            onBlockDrop={onBlockDrop}
            properties={{ percentage: 5.0 }}
          />
          <BlockItem
            type={BlockType.PERCENTAGE_DOWN}
            title="Percentage Down"
            icon={<TrendingDown className="h-4 w-4" />}
            description="Triggers when price moves down by specified percentage"
            onBlockDrop={onBlockDrop}
            properties={{ percentage: 10.0 }}
          />
        </AccordionContent>
      </AccordionItem>

      <AccordionItem value="output">
        <AccordionTrigger className="py-2">Output Signals</AccordionTrigger>
        <AccordionContent>
          <BlockItem
            type={BlockType.TRIGGER}
            title="Signal"
            icon={<Target className="h-4 w-4" />}
            description="Bullish, Bearish, or Neutral signal"
            onBlockDrop={onBlockDrop}
            properties={{ signal: 'bullish', confidence: 75 }}
          />
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

export default BlockPalette;
