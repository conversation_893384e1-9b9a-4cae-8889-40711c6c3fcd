import React, { useState } from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { BarChart2, Trash2, Star, Settings } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface IndicatorBlockProps {
  data: {
    id: string;
    indicator: string;
    parameters: Record<string, any>;
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
    hasError?: boolean;
    isDisconnected?: boolean;
    errorMessages?: string[];
  };
  selected: boolean;
}

const IndicatorBlock: React.FC<IndicatorBlockProps> = ({ data, selected }) => {
  const [isEditing, setIsEditing] = useState(false);

  // Available indicators
  const indicators = [
    { value: 'rsi', label: 'RSI', defaultParams: { period: 14 } },
    { value: 'macd', label: 'MACD', defaultParams: { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 } },
    { value: 'bollinger', label: 'Bollinger Bands', defaultParams: { period: 20, stdDev: 2 } },
    { value: 'sma', label: 'Simple Moving Average', defaultParams: { period: 20 } },
    { value: 'ema', label: 'Exponential Moving Average', defaultParams: { period: 20 } },
    { value: 'stochastic', label: 'Stochastic Oscillator', defaultParams: { kPeriod: 14, dPeriod: 3 } },
    { value: 'support', label: 'Support Level', defaultParams: { timeframe: 'month', strength: 2 } },
    { value: 'resistance', label: 'Resistance Level', defaultParams: { timeframe: 'month', strength: 2 } },
    { value: 'candle_pattern', label: 'Candle Pattern', defaultParams: { timeframe: 'daily', pattern: 'any' } }
  ];

  // Get the current indicator
  const currentIndicator = indicators.find(ind => ind.value === data.indicator) || indicators[0];

  // Handle indicator change
  const handleIndicatorChange = (value: string) => {
    const newIndicator = indicators.find(ind => ind.value === value);
    if (newIndicator) {
      data.onUpdate({
        indicator: value,
        parameters: newIndicator.defaultParams
      });
    }
  };

  // Handle parameter change
  const handleParameterChange = (key: string, value: string) => {
    // For timeframe and pattern, keep as string; for others, convert to number
    if (key === 'timeframe' || key === 'pattern') {
      data.onUpdate({
        parameters: {
          ...data.parameters,
          [key]: value
        }
      });
    } else {
      const numValue = parseFloat(value);
      if (!isNaN(numValue)) {
        data.onUpdate({
          parameters: {
            ...data.parameters,
            [key]: numValue
          }
        });
      }
    }
  };

  // Render parameters based on the selected indicator
  const renderParameters = () => {
    if (!isEditing) return null;

    switch (data.indicator) {
      case 'rsi':
        return (
          <div className="mt-2">
            <label className="text-xs font-medium block mb-1">Period</label>
            <Input
              type="number"
              value={data.parameters.period || 14}
              onChange={e => handleParameterChange('period', e.target.value)}
              className="h-8 text-xs"
              min={1}
            />
          </div>
        );
      case 'macd':
        return (
          <div className="space-y-2 mt-2">
            <div>
              <label className="text-xs font-medium block mb-1">Fast Period</label>
              <Input
                type="number"
                value={data.parameters.fastPeriod || 12}
                onChange={e => handleParameterChange('fastPeriod', e.target.value)}
                className="h-8 text-xs"
                min={1}
              />
            </div>
            <div>
              <label className="text-xs font-medium block mb-1">Slow Period</label>
              <Input
                type="number"
                value={data.parameters.slowPeriod || 26}
                onChange={e => handleParameterChange('slowPeriod', e.target.value)}
                className="h-8 text-xs"
                min={1}
              />
            </div>
            <div>
              <label className="text-xs font-medium block mb-1">Signal Period</label>
              <Input
                type="number"
                value={data.parameters.signalPeriod || 9}
                onChange={e => handleParameterChange('signalPeriod', e.target.value)}
                className="h-8 text-xs"
                min={1}
              />
            </div>
          </div>
        );
      case 'bollinger':
        return (
          <div className="space-y-2 mt-2">
            <div>
              <label className="text-xs font-medium block mb-1">Period</label>
              <Input
                type="number"
                value={data.parameters.period || 20}
                onChange={e => handleParameterChange('period', e.target.value)}
                className="h-8 text-xs"
                min={1}
              />
            </div>
            <div>
              <label className="text-xs font-medium block mb-1">Standard Deviation</label>
              <Input
                type="number"
                value={data.parameters.stdDev || 2}
                onChange={e => handleParameterChange('stdDev', e.target.value)}
                className="h-8 text-xs"
                min={0.1}
                step={0.1}
              />
            </div>
          </div>
        );
      case 'sma':
      case 'ema':
        return (
          <div className="mt-2">
            <label className="text-xs font-medium block mb-1">Period</label>
            <Input
              type="number"
              value={data.parameters.period || 20}
              onChange={e => handleParameterChange('period', e.target.value)}
              className="h-8 text-xs"
              min={1}
            />
          </div>
        );
      case 'stochastic':
        return (
          <div className="space-y-2 mt-2">
            <div>
              <label className="text-xs font-medium block mb-1">%K Period</label>
              <Input
                type="number"
                value={data.parameters.kPeriod || 14}
                onChange={e => handleParameterChange('kPeriod', e.target.value)}
                className="h-8 text-xs"
                min={1}
              />
            </div>
            <div>
              <label className="text-xs font-medium block mb-1">%D Period</label>
              <Input
                type="number"
                value={data.parameters.dPeriod || 3}
                onChange={e => handleParameterChange('dPeriod', e.target.value)}
                className="h-8 text-xs"
                min={1}
              />
            </div>
          </div>
        );
      case 'support':
      case 'resistance':
        return (
          <div className="space-y-2 mt-2">
            <div>
              <label className="text-xs font-medium block mb-1">Timeframe</label>
              <select
                value={data.parameters.timeframe || 'month'}
                onChange={e => handleParameterChange('timeframe', e.target.value)}
                className="w-full h-8 text-xs border border-input rounded-md px-2 bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
              >
                <option value="week">Last Week</option>
                <option value="month">Last Month</option>
                <option value="3month">Last 3 Months</option>
                <option value="6month">Last 6 Months</option>
                <option value="year">Last Year</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">Historical period to analyze</p>
            </div>
            <div>
              <label className="text-xs font-medium block mb-1">Strength</label>
              <Input
                type="number"
                value={data.parameters.strength || 2}
                onChange={e => handleParameterChange('strength', e.target.value)}
                className="h-8 text-xs"
                min={2}
                max={10}
              />
              <p className="text-xs text-gray-500 mt-1">Minimum touches required</p>
            </div>
          </div>
        );
      case 'candle_pattern':
        return (
          <div className="space-y-2 mt-2">
            <div>
              <label className="text-xs font-medium block mb-1">Timeframe</label>
              <select
                value={data.parameters.timeframe || 'daily'}
                onChange={e => handleParameterChange('timeframe', e.target.value)}
                className="w-full h-8 text-xs border border-input rounded-md px-2 bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
              >
                <option value="1min">1 Minute</option>
                <option value="5min">5 Minutes</option>
                <option value="15min">15 Minutes</option>
                <option value="1hour">1 Hour</option>
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">Candle timeframe to analyze</p>
            </div>
            <div>
              <label className="text-xs font-medium block mb-1">Pattern Type</label>
              <select
                value={data.parameters.pattern || 'any'}
                onChange={e => handleParameterChange('pattern', e.target.value)}
                className="w-full h-8 text-xs border border-input rounded-md px-2 bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
              >
                <option value="any">Any Pattern</option>
                <optgroup label="Reversal Patterns">
                  <option value="doji">Doji</option>
                  <option value="hammer">Hammer</option>
                  <option value="shooting_star">Shooting Star</option>
                  <option value="engulfing">Engulfing</option>
                  <option value="morning_star">Morning Star</option>
                  <option value="evening_star">Evening Star</option>
                </optgroup>
                <optgroup label="Continuation Patterns">
                  <option value="spinning_top">Spinning Top</option>
                  <option value="marubozu">Marubozu</option>
                </optgroup>
                <optgroup label="Pattern Categories">
                  <option value="bullish">Any Bullish Pattern</option>
                  <option value="bearish">Any Bearish Pattern</option>
                  <option value="reversal">Any Reversal Pattern</option>
                </optgroup>
              </select>
              <p className="text-xs text-gray-500 mt-1">Specific pattern to detect</p>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  // Render error messages
  const renderErrorMessages = () => {
    if (!data.hasError || !data.errorMessages || data.errorMessages.length === 0) {
      return null;
    }

    return (
      <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
        <div className="flex items-center text-red-700 text-xs font-medium mb-1">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          Error
        </div>
        <ul className="list-disc pl-5 text-xs text-red-600">
          {data.errorMessages.map((error, index) => (
            <li key={index}>{error}</li>
          ))}
        </ul>
      </div>
    );
  };

  return (
    <div className={`relative ${data.isDisconnected ? 'disconnected-block' : ''}`}>
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 border-2 border-white/20 bg-[#1A1A1A] hover:border-blue-400 hover:bg-blue-400/20 transition-all duration-200"
        style={{
          left: -6,
          background: data.hasError ? 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)' : 'linear-gradient(135deg, #1A1A1A 0%, #141414 100%)',
          boxShadow: '0 0 0 2px rgba(255,255,255,0.1), 0 2px 4px rgba(0,0,0,0.2)'
        }}
      />

      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 border-2 border-white/20 bg-[#1A1A1A] hover:border-blue-400 hover:bg-blue-400/20 transition-all duration-200"
        style={{
          right: -6,
          background: data.hasError ? 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)' : 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
          boxShadow: '0 0 0 2px rgba(255,255,255,0.1), 0 2px 4px rgba(0,0,0,0.2)'
        }}
      />

      <div className={`group relative w-72 transition-all duration-200 ${selected ? 'scale-105' : 'hover:scale-[1.02]'}`}>
        {/* Main card container with modern styling */}
        <div className={`
          relative overflow-hidden rounded-2xl border backdrop-blur-sm transition-all duration-200
          ${selected
            ? 'border-purple-400/50 shadow-lg shadow-purple-500/20 bg-gradient-to-br from-[#1A1A1A]/90 to-[#141414]/80'
            : 'border-white/10 hover:border-white/20 bg-gradient-to-br from-[#1A1A1A]/80 to-[#141414]/60 hover:shadow-lg hover:shadow-black/20'
          }
          ${data.isEntryBlock ? 'ring-2 ring-purple-400/30' : ''}
          ${data.hasError ? 'border-red-400/50 ring-2 ring-red-400/30' : ''}
        `}>

          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/[0.02] to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200" />

          {/* Header */}
          <div className="relative p-4 pb-3 flex items-center justify-between border-b border-white/5">
            <div className="flex items-center gap-3">
              {/* Icon with modern styling */}
              <div className="relative">
                <div className={`w-8 h-8 rounded-xl border flex items-center justify-center ${
                  data.hasError
                    ? 'bg-gradient-to-br from-red-500/20 to-red-600/10 border-red-400/20'
                    : 'bg-gradient-to-br from-purple-500/20 to-purple-600/10 border-purple-400/20'
                }`}>
                  <BarChart2 className={`h-4 w-4 ${data.hasError ? 'text-red-400' : 'text-purple-400'}`} />
                </div>
                {data.isEntryBlock && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-purple-400 border-2 border-[#1A1A1A]" />
                )}
              </div>

              {/* Title */}
              <div>
                <h3 className="text-sm font-semibold text-white/90 group-hover:text-white transition-colors duration-200">
                  Technical Indicator
                </h3>
                <p className="text-xs text-white/50 mt-0.5">
                  {currentIndicator.label}
                </p>
              </div>
            </div>

            {/* Action buttons */}
            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              {!data.isEntryBlock && (
                <button
                  onClick={data.onSetAsEntry}
                  className="w-7 h-7 rounded-lg bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 flex items-center justify-center transition-all duration-200"
                  title="Set as entry block"
                >
                  <Star className="h-3 w-3 text-white/60 hover:text-white" />
                </button>
              )}
              <button
                onClick={() => setIsEditing(!isEditing)}
                className="w-7 h-7 rounded-lg bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 flex items-center justify-center transition-all duration-200"
                title={isEditing ? "Close settings" : "Settings"}
              >
                <Settings className="h-3 w-3 text-white/60 hover:text-white" />
              </button>
              <button
                onClick={data.onRemove}
                className="w-7 h-7 rounded-lg bg-red-500/10 hover:bg-red-500/20 border border-red-400/20 hover:border-red-400/40 flex items-center justify-center transition-all duration-200"
                title="Remove block"
              >
                <Trash2 className="h-3 w-3 text-red-400" />
              </button>
            </div>
          </div>
          {/* Content */}
          <div className="relative p-4 pt-3 space-y-3">
            <div>
              <label className="text-xs font-medium text-white/70 block mb-2">Indicator</label>
              <Select
                value={data.indicator}
                onValueChange={handleIndicatorChange}
              >
                <SelectTrigger className="h-9 text-xs bg-white/5 border-white/10 hover:border-white/20 focus:border-purple-400/50 transition-colors duration-200">
                  <SelectValue placeholder="Select indicator" />
                </SelectTrigger>
                <SelectContent className="bg-[#1A1A1A] border-white/10">
                  {indicators.map(indicator => (
                    <SelectItem key={indicator.value} value={indicator.value} className="text-xs text-white/90 hover:bg-white/10">
                      {indicator.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {renderParameters()}

            {/* Display error messages */}
            {renderErrorMessages()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default IndicatorBlock;
