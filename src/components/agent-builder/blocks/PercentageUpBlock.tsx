import React, { useState } from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Settings, Trash2, Star, TrendingUp } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface PercentageUpBlockProps {
  data: {
    id: string;
    percentage: number;
    inputConnections: string[];
    outputConnections: string[];
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
  };
  selected: boolean;
}

const PercentageUpBlock: React.FC<PercentageUpBlockProps> = ({ data, selected }) => {
  const [isEditing, setIsEditing] = useState(false);

  // Handle percentage change
  const handlePercentageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value) || 0;
    data.onUpdate({ percentage: Math.max(0, value) });
  };

  return (
    <div className="relative">
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-blue-500 border-2 border-white"
      />

      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 bg-green-500 border-2 border-white"
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-green-500/10 flex items-center justify-center">
              <TrendingUp className="h-3 w-3 text-green-500" />
            </div>
            <CardTitle className="text-sm font-medium">+{data.percentage}%</CardTitle>
          </div>
          <div className="flex gap-1">
            {!data.isEntryBlock && (
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={data.onSetAsEntry}
                title="Set as entry block"
              >
                <Star className="h-3 w-3" />
              </Button>
            )}
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={() => setIsEditing(!isEditing)}
              title="Edit block"
            >
              <Settings className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-red-500 hover:text-red-700"
              onClick={data.onRemove}
              title="Remove block"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="p-3 pt-2">
          <div className="space-y-2">
            <div className="text-xs text-muted-foreground">
              Triggers when price moves up by {data.percentage}%
            </div>

            {isEditing && (
              <div className="space-y-3 pt-2 border-t">
                <div className="space-y-2">
                  <Label htmlFor="percentage" className="text-xs">Percentage Threshold</Label>
                  <Input
                    id="percentage"
                    type="number"
                    min="0"
                    step="0.1"
                    value={data.percentage}
                    onChange={handlePercentageChange}
                    className="h-8 text-xs"
                    placeholder="e.g., 5.0"
                  />
                  <p className="text-xs text-muted-foreground">
                    Enter the percentage gain that will trigger this block (e.g., 5 for +5%)
                  </p>
                </div>
              </div>
            )}

            {isEditing && (
              <div className="mt-2 p-2 bg-muted/50 rounded-md">
                <p className="text-xs text-muted-foreground">
                  This is an action block that triggers when the stock price increases by the specified percentage. 
                  Unlike trigger blocks, this allows the agent to continue executing additional blocks after the percentage move occurs.
                </p>
                <div className="flex items-center gap-2 mt-2">
                  <TrendingUp className="h-3 w-3 text-green-500" />
                  <span className="text-xs font-medium text-green-600">
                    Triggers on +{data.percentage}% price movement
                  </span>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PercentageUpBlock;
