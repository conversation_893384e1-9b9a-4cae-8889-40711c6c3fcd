import React, { useState } from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { DollarSign, Trash2, Star } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface PriceBlockProps {
  data: {
    id: string;
    dataPoint: string;
    timeframe?: string;
    lookback?: number;
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
  };
  selected: boolean;
}

const PriceBlock: React.FC<PriceBlockProps> = ({ data, selected }) => {
  const [isEditing, setIsEditing] = useState(false);

  // Available price data points
  const dataPoints = [
    { value: 'open', label: 'Open Price' },
    { value: 'high', label: 'High Price' },
    { value: 'low', label: 'Low Price' },
    { value: 'close', label: 'Close Price' },
    { value: 'volume', label: 'Volume' },
    { value: 'current', label: 'Current Price' }
  ];

  // Available timeframes
  const timeframes = [
    { value: 'day', label: 'Daily' },
    { value: 'hour', label: 'Hourly' },
    { value: '15minute', label: '15 Minutes' }
  ];

  // Handle data point change
  const handleDataPointChange = (value: string) => {
    data.onUpdate({ dataPoint: value });
  };

  // Handle timeframe change
  const handleTimeframeChange = (value: string) => {
    data.onUpdate({ timeframe: value });
  };

  // Handle lookback change
  const handleLookbackChange = (value: string) => {
    const numValue = parseInt(value);
    if (!isNaN(numValue) && numValue >= 0) {
      data.onUpdate({ lookback: numValue });
    }
  };

  return (
    <div className="relative">
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-2.5 h-2.5 border border-white/10 bg-[#1A1A1A]/80 hover:border-white/30 hover:bg-white/5 transition-all duration-300"
        style={{
          left: -5,
          background: 'rgba(26, 26, 26, 0.9)',
          boxShadow: '0 0 0 1px rgba(255,255,255,0.05), 0 1px 3px rgba(0,0,0,0.2)'
        }}
      />

      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-2.5 h-2.5 border border-white/10 bg-[#1A1A1A]/80 hover:border-white/30 hover:bg-white/5 transition-all duration-300"
        style={{
          right: -5,
          background: 'rgba(26, 26, 26, 0.9)',
          boxShadow: '0 0 0 1px rgba(255,255,255,0.05), 0 1px 3px rgba(0,0,0,0.2)'
        }}
      />

      <div className={`group relative w-72 transition-all duration-200 ${selected ? 'scale-105' : 'hover:scale-[1.02]'}`}>
        {/* Main card container with modern styling */}
        <div className={`
          relative overflow-hidden rounded-2xl border backdrop-blur-sm transition-all duration-200
          ${selected
            ? 'border-blue-400/50 shadow-lg shadow-blue-500/20 bg-gradient-to-br from-[#1A1A1A]/90 to-[#141414]/80'
            : 'border-white/10 hover:border-white/20 bg-gradient-to-br from-[#1A1A1A]/80 to-[#141414]/60 hover:shadow-lg hover:shadow-black/20'
          }
          ${data.isEntryBlock ? 'ring-2 ring-blue-400/30' : ''}
        `}>

          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/[0.02] to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200" />

          {/* Header */}
          <div className="relative p-4 pb-3 flex items-center justify-between border-b border-white/5">
            <div className="flex items-center gap-3">
              {/* Icon with modern styling */}
              <div className="relative">
                <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-blue-500/20 to-blue-600/10 border border-blue-400/20 flex items-center justify-center">
                  <DollarSign className="h-4 w-4 text-blue-400" />
                </div>
                {data.isEntryBlock && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-blue-400 border-2 border-[#1A1A1A]" />
                )}
              </div>

              {/* Title */}
              <div>
                <h3 className="text-sm font-semibold text-white/90 group-hover:text-white transition-colors duration-200">
                  Price Data
                </h3>
                <p className="text-xs text-white/50 mt-0.5">
                  Market price information
                </p>
              </div>
            </div>

            {/* Action buttons */}
            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              {!data.isEntryBlock && (
                <button
                  onClick={data.onSetAsEntry}
                  className="w-7 h-7 rounded-lg bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 flex items-center justify-center transition-all duration-200"
                  title="Set as entry block"
                >
                  <Star className="h-3 w-3 text-white/60 hover:text-white" />
                </button>
              )}
              <button
                onClick={() => setIsEditing(!isEditing)}
                className="w-7 h-7 rounded-lg bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 flex items-center justify-center transition-all duration-200"
                title={isEditing ? "Close editor" : "Edit block"}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-3 w-3 text-white/60 hover:text-white"
                >
                  <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z" />
                  <path d="m15 5 4 4" />
                </svg>
              </button>
              <button
                onClick={data.onRemove}
                className="w-7 h-7 rounded-lg bg-red-500/10 hover:bg-red-500/20 border border-red-400/20 hover:border-red-400/40 flex items-center justify-center transition-all duration-200"
                title="Remove block"
              >
                <Trash2 className="h-3 w-3 text-red-400" />
              </button>
            </div>
          </div>
          {/* Content */}
          <div className="relative p-4 pt-3 space-y-3">
            <div>
              <label className="text-xs font-medium text-white/70 block mb-2">Data Point</label>
              <Select
                value={data.dataPoint}
                onValueChange={handleDataPointChange}
              >
                <SelectTrigger className="h-9 text-xs bg-white/5 border-white/10 hover:border-white/20 focus:border-blue-400/50 transition-colors duration-200">
                  <SelectValue placeholder="Select data point" />
                </SelectTrigger>
                <SelectContent className="bg-[#1A1A1A] border-white/10">
                  {dataPoints.map(point => (
                    <SelectItem key={point.value} value={point.value} className="text-xs text-white/90 hover:bg-white/10">
                      {point.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {isEditing && (
              <div className="space-y-3 pt-2 border-t border-white/5">
                <div>
                  <label className="text-xs font-medium text-white/70 block mb-2">Timeframe</label>
                  <Select
                    value={data.timeframe || 'day'}
                    onValueChange={handleTimeframeChange}
                  >
                    <SelectTrigger className="h-9 text-xs bg-white/5 border-white/10 hover:border-white/20 focus:border-blue-400/50 transition-colors duration-200">
                      <SelectValue placeholder="Select timeframe" />
                    </SelectTrigger>
                    <SelectContent className="bg-[#1A1A1A] border-white/10">
                      {timeframes.map(tf => (
                        <SelectItem key={tf.value} value={tf.value} className="text-xs text-white/90 hover:bg-white/10">
                          {tf.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-xs font-medium text-white/70 block mb-2">Lookback Periods</label>
                  <Input
                    type="number"
                    value={data.lookback || 0}
                    onChange={e => handleLookbackChange(e.target.value)}
                    className="h-9 text-xs bg-white/5 border-white/10 hover:border-white/20 focus:border-blue-400/50 transition-colors duration-200"
                    min={0}
                    placeholder="0 for current data"
                  />
                  <p className="text-xs text-white/40 mt-1.5">
                    {data.lookback === 0 || !data.lookback
                      ? "Using current data"
                      : `Looking back ${data.lookback} periods`}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PriceBlock;
