import React, { useState } from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { DollarSign, Trash2, Star } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface PriceBlockProps {
  data: {
    id: string;
    dataPoint: string;
    timeframe?: string;
    lookback?: number;
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
  };
  selected: boolean;
}

const PriceBlock: React.FC<PriceBlockProps> = ({ data, selected }) => {
  const [isEditing, setIsEditing] = useState(false);

  // Available price data points
  const dataPoints = [
    { value: 'open', label: 'Open Price' },
    { value: 'high', label: 'High Price' },
    { value: 'low', label: 'Low Price' },
    { value: 'close', label: 'Close Price' },
    { value: 'volume', label: 'Volume' },
    { value: 'current', label: 'Current Price' }
  ];

  // Available timeframes
  const timeframes = [
    { value: 'day', label: 'Daily' },
    { value: 'hour', label: 'Hourly' },
    { value: '15minute', label: '15 Minutes' }
  ];

  // Handle data point change
  const handleDataPointChange = (value: string) => {
    data.onUpdate({ dataPoint: value });
  };

  // Handle timeframe change
  const handleTimeframeChange = (value: string) => {
    data.onUpdate({ timeframe: value });
  };

  // Handle lookback change
  const handleLookbackChange = (value: string) => {
    const numValue = parseInt(value);
    if (!isNaN(numValue) && numValue >= 0) {
      data.onUpdate({ lookback: numValue });
    }
  };

  return (
    <div className="relative">
      {/* Premium input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 border border-white/[0.08] bg-[#1A1A1A]/90 transition-all duration-500"
        style={{
          left: -6,
          background: 'linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(20, 20, 20, 0.9) 100%)',
          boxShadow: '0 0 0 1px rgba(255,255,255,0.03), 0 2px 8px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.05)'
        }}
      />

      {/* Premium output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 border border-emerald-400/20 bg-emerald-500/10 transition-all duration-500"
        style={{
          right: -6,
          background: 'linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(5, 150, 105, 0.1) 100%)',
          boxShadow: '0 0 0 1px rgba(16, 185, 129, 0.2), 0 2px 8px rgba(0,0,0,0.3), 0 0 12px rgba(16, 185, 129, 0.1)'
        }}
      />

      <div className={`group relative w-80 transition-all duration-500 ease-out ${selected ? 'scale-[1.02]' : ''}`}>
        {/* Premium card container with sophisticated styling */}
        <div className={`
          relative overflow-hidden rounded-2xl backdrop-blur-xl transition-all duration-500 ease-out
          ${selected
            ? 'border border-emerald-400/30 shadow-2xl shadow-emerald-500/10 bg-gradient-to-br from-[#1A1A1A]/95 to-[#141414]/90'
            : 'border border-white/[0.08] bg-gradient-to-br from-[#1A1A1A]/85 to-[#141414]/75'
          }
          ${data.isEntryBlock ? 'ring-1 ring-emerald-400/20 shadow-lg shadow-emerald-500/5' : ''}
        `}>

          {/* Subtle premium overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/[0.01] via-transparent to-black/[0.02]" />

          {/* Header with refined spacing */}
          <div className="relative px-5 py-4 border-b border-white/[0.04]">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                {/* Premium icon container */}
                <div className="relative">
                  <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-emerald-500/15 to-emerald-600/8 border border-emerald-400/20 flex items-center justify-center shadow-inner">
                    <DollarSign className="h-5 w-5 text-emerald-400/90" />
                  </div>
                  {data.isEntryBlock && (
                    <div className="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-emerald-400 border-2 border-[#1A1A1A] shadow-lg" />
                  )}
                </div>

                {/* Refined typography */}
                <div>
                  <h3 className="text-sm font-medium text-white/85 tracking-wide">
                    Price Data
                  </h3>
                  <p className="text-xs text-white/45 mt-1 font-light tracking-wide">
                    Market price information
                  </p>
                </div>
              </div>

              {/* Minimal action buttons */}
              <div className="flex items-center gap-1">
                {!data.isEntryBlock && (
                  <button
                    onClick={data.onSetAsEntry}
                    className="w-8 h-8 rounded-lg bg-white/[0.03] border border-white/[0.06] flex items-center justify-center transition-all duration-300"
                    title="Set as entry block"
                  >
                    <Star className="h-3.5 w-3.5 text-white/50" />
                  </button>
                )}
                <button
                  onClick={() => setIsEditing(!isEditing)}
                  className="w-8 h-8 rounded-lg bg-white/[0.03] border border-white/[0.06] flex items-center justify-center transition-all duration-300"
                  title={isEditing ? "Close editor" : "Edit block"}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-3.5 w-3.5 text-white/50"
                  >
                    <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z" />
                    <path d="m15 5 4 4" />
                  </svg>
                </button>
                <button
                  onClick={data.onRemove}
                  className="w-8 h-8 rounded-lg bg-red-500/[0.08] border border-red-400/[0.15] flex items-center justify-center transition-all duration-300"
                  title="Remove block"
                >
                  <Trash2 className="h-3.5 w-3.5 text-red-400/80" />
                </button>
              </div>
            </div>
          </div>
          {/* Premium content area */}
          <div className="relative px-5 py-4 space-y-4">
            <div>
              <label className="text-xs font-medium text-white/60 block mb-3 uppercase tracking-wider">Data Point</label>
              <Select
                value={data.dataPoint}
                onValueChange={handleDataPointChange}
              >
                <SelectTrigger className="h-10 text-sm bg-white/[0.02] border-white/[0.08] focus:border-emerald-400/30 transition-all duration-300 rounded-xl">
                  <SelectValue placeholder="Select data point" />
                </SelectTrigger>
                <SelectContent className="bg-[#1A1A1A]/95 border-white/[0.08] backdrop-blur-xl rounded-xl">
                  {dataPoints.map(point => (
                    <SelectItem key={point.value} value={point.value} className="text-sm text-white/85 focus:bg-emerald-400/10">
                      {point.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {isEditing && (
              <div className="space-y-4 pt-4 border-t border-white/[0.04]">
                <div>
                  <label className="text-xs font-medium text-white/60 block mb-3 uppercase tracking-wider">Timeframe</label>
                  <Select
                    value={data.timeframe || 'day'}
                    onValueChange={handleTimeframeChange}
                  >
                    <SelectTrigger className="h-10 text-sm bg-white/[0.02] border-white/[0.08] focus:border-emerald-400/30 transition-all duration-300 rounded-xl">
                      <SelectValue placeholder="Select timeframe" />
                    </SelectTrigger>
                    <SelectContent className="bg-[#1A1A1A]/95 border-white/[0.08] backdrop-blur-xl rounded-xl">
                      {timeframes.map(tf => (
                        <SelectItem key={tf.value} value={tf.value} className="text-sm text-white/85 focus:bg-emerald-400/10">
                          {tf.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-xs font-medium text-white/60 block mb-3 uppercase tracking-wider">Lookback Periods</label>
                  <Input
                    type="number"
                    value={data.lookback || 0}
                    onChange={e => handleLookbackChange(e.target.value)}
                    className="h-10 text-sm bg-white/[0.02] border-white/[0.08] focus:border-emerald-400/30 transition-all duration-300 rounded-xl"
                    min={0}
                    placeholder="0 for current data"
                  />
                  <p className="text-xs text-white/35 mt-2 font-light tracking-wide">
                    {data.lookback === 0 || !data.lookback
                      ? "Using current data"
                      : `Looking back ${data.lookback} periods`}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PriceBlock;
