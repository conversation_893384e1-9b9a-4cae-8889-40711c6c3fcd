import * as React from "react";
import { useState, useEffect, useRef } from "react";
import axios from "axios";
import { Paperclip, Globe, Send, Loader2, MessageSquare, BarChart3, Code2, Layout, HelpCircle, Search, Brain, User, TrendingUp, Bitcoin, DollarSign, Newspaper, BookOpen, PieChart, ArrowUp, X } from "lucide-react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useNavigate, useParams } from "react-router-dom";
import ModelSelector from "./ModelSelector";
import MessageCard from './MessageCard';
import TypingEffect from "@/components/ui/typing-effect";
import { AnalysisState } from './types';
import { cn } from "@/lib/utils";
import {
  Pop<PERSON>,
  <PERSON>overContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { WelcomeHeading } from "@/components/ui/WelcomeHeading";
import MessageCounter from './MessageCounter';
import LoadingMessage from './LoadingMessage';
import { useUserLimits } from '@/hooks/useUserLimits';
import { useAuth } from '@/contexts/AuthContext';

// =============================================
// TYPE DEFINITIONS
// =============================================
interface Message {
  role: string;
  content: {
    text: string | null;
    marketData?: any;
    webResults?: any[];
    symbol?: string | null;
    searchQueries?: string[];
    evaAnalysis?: any;
    symbols?: string[];
    isLoading?: boolean;
    analysisProgress?: any;
    aiAnalysis?: string;
    symbolTypes?: any;
    loadingPlaceholder?: boolean;
    isGeneralMessage?: boolean;
  };
}

interface UserTokens {
  user_id: string;
  tokens_remaining: number;
  tokens_used: number;
  reset_date: string;
}

interface MarketData {
  price?: number;
  change?: number;
  changePercent?: number;
  volume?: number;
  // Add other market data fields as needed
}

// Update ChatAIResponse interface
interface ChatAIResponse {
  symbols: string[];
  symbolTypes: { [key: string]: 'STOCK' | 'CRYPTO' | 'MARKET' };
  marketData: any;
  isGeneralMessage: boolean;
}

// =============================================
// CONSTANTS
// =============================================
const QUICK_PROMPTS = [
  {
    text: "Is NVDA overvalued?",
    category: "Finance",
    icon: <TrendingUp className="w-4 h-4 text-emerald-400" />
  },
  {
    text: "Would Cathie Wood double down on TSLA today?",
    category: "Crypto",
    icon: <Bitcoin className="w-4 h-4 text-orange-400" />
  },
  {
    text: "Should I long or short PLTR?",
    category: "News",
    icon: <Newspaper className="w-4 h-4 text-blue-400" />
  },
  {
    text: "Would Warren Buffett buy AAPL?",
    category: "Education",
    icon: <BookOpen className="w-4 h-4 text-purple-400" />
  },
  {
    text: "Teach me how to trade options",
    category: "Investment",
    icon: <PieChart className="w-4 h-4 text-cyan-400" />
  }
];

// =============================================
// MAIN CHAT INTERFACE COMPONENT
// =============================================
const ChatInterface: React.FC = () => {
  // Define animations for smooth transitions
  const animationStyles = `
    @keyframes reveal {
      0% {
        opacity: 0;
        transform: scale(0.98);
      }
      100% {
        opacity: 1;
        transform: scale(1);
      }
    }

    .animate-reveal {
      animation: reveal 400ms cubic-bezier(0.22, 1, 0.36, 1) forwards;
    }

    @keyframes popIn {
      0% {
        opacity: 0;
        transform: scale(0.96);
      }
      70% {
        opacity: 1;
        transform: scale(1.02);
      }
      100% {
        opacity: 1;
        transform: scale(1);
      }
    }

    .animate-pop-in {
      animation: popIn 400ms cubic-bezier(0.22, 1, 0.36, 1) forwards;
    }

    /* Staggered animations for search results */
    .search-result-item:nth-child(1) {
      animation-delay: 100ms;
    }

    .search-result-item:nth-child(2) {
      animation-delay: 200ms;
    }

    .search-result-item:nth-child(3) {
      animation-delay: 300ms;
    }

    /* Animation for response content sections */
    .response-section {
      opacity: 0;
      animation: sectionFadeIn 500ms ease forwards;
    }

    @keyframes sectionFadeIn {
      0% {
        opacity: 0;
        transform: translateY(8px);
      }
      100% {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Staggered animations for different sections */
    .response-section:nth-child(1) {
      animation-delay: 150ms;
    }

    .response-section:nth-child(2) {
      animation-delay: 300ms;
    }

    .response-section:nth-child(3) {
      animation-delay: 450ms;
    }

    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    .animate-fade-in {
      animation: fadeIn 300ms ease forwards;
    }
  `;

  // =============================================
  // STATE VARIABLES
  // =============================================
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [slidePosition, setSlidePosition] = useState(0);
  const [userTokens, setUserTokens] = useState<UserTokens | null>(null);

  // Update these loading state variables to default to false
  const [isAuthLoading, setIsAuthLoading] = useState(false);

  // Analysis loading states for different data types
  const [analysisState, setAnalysisState] = useState<AnalysisState>({
    isLoadingMarketData: false,
    isLoadingNews: false,
    isLoadingAnalysis: false,
    isLoadingAI: false
  });

  // Skip welcome screen by default
  const [shouldSkipWelcome, setShouldSkipWelcome] = useState(true);

  // Get authentication state from useAuth hook
  const { isAuthenticated, isNewUser } = useAuth();

  // Add useUserLimits hook
  const {
    messagesRemaining,
    messagesLimit,
    incrementMessagesUsed,
    isLoading: isLoadingLimits,
    planType,
    subscription,
    refreshLimits,
    hasReachedLimit
  } = useUserLimits();

  // Set this to false to avoid loading screen
  const [isCheckingOnboarding, setIsCheckingOnboarding] = useState(false);

  // Add a new state to store the user's name
  const [userName, setUserName] = useState<string>("");

  // =============================================
  // REFS, HOOKS, AND NAVIGATION
  // =============================================
  const fileInputRef = useRef<HTMLTextAreaElement>(null);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { id: currentChatId } = useParams();

  // Reset state for new chats
  useEffect(() => {
    if (!currentChatId) {
      // This is a new chat, reset all states
      setMessages([]);
      setIsLoading(false);
    }
  }, [currentChatId]);

  // Update the auth check effect
  useEffect(() => {
    const checkAuth = async () => {
      try {
      } catch (error) {
      } finally {
        setIsAuthLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Restore the chat loading effect
  useEffect(() => {
    if (!currentChatId || !isAuthenticated) {
      setMessages([]);
      return;
    }

    const loadChat = async () => {
      try {
        const { data: chatMessages, error } = await supabase
          .from('messages')
          .select('*')
          .eq('chat_id', currentChatId)
          .order('created_at', { ascending: true });

        if (error) {
          throw error;
        }

        if (chatMessages) {
          const formattedMessages = chatMessages.map(msg => ({
            role: msg.role,
            content: typeof msg.content === 'string' ? JSON.parse(msg.content) : msg.content
          }));
          setMessages(formattedMessages);
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load chat messages. Please try refreshing the page.",
          variant: "destructive"
        });
      }
    };

    loadChat();
  }, [currentChatId, isAuthenticated]);

  // Update the welcome screen check effect
  useEffect(() => {
    setIsCheckingOnboarding(true);
    // Check URL parameters and hash for auth tokens
    const skipWelcome = new URLSearchParams(window.location.search).get('skipWelcome') === 'true';
    const hasAuthToken = window.location.hash.includes('access_token=');

    // Force show welcome screen when new empty chat, unless explicitly skipped
    const shouldShowWelcome = (!currentChatId || currentChatId === 'new') && !(skipWelcome || hasAuthToken);

    setShouldSkipWelcome(!shouldShowWelcome);
    setIsCheckingOnboarding(false);

    // Clean up hash if it contains auth tokens
    if (hasAuthToken) {
      // Remove the hash without causing a page reload
      const cleanUrl = window.location.href.split('#')[0];
      window.history.replaceState({}, document.title, cleanUrl);
    }
  }, [currentChatId, isAuthenticated]);

  // =============================================
  // QUICK PROMPTS ANIMATION EFFECT
  // =============================================
  const [carouselWidth, setCarouselWidth] = useState(0);

  // Update the carousel animation effect
  useEffect(() => {
    let animationInterval: NodeJS.Timeout;

    const initializeCarousel = () => {
      const carousel = document.querySelector('.quick-prompts-carousel');
      if (carousel) {
        const totalWidth = carousel.scrollWidth / 2; // Divide by 2 because we duplicated the items
        setCarouselWidth(totalWidth);

        // Start the animation with a faster speed
        const animationSpeed = 15; // Lower = faster
        animationInterval = setInterval(() => {
          setSlidePosition(prev => {
            const newPosition = prev - 1;
            return newPosition <= -totalWidth ? 0 : newPosition;
          });
        }, animationSpeed);
      }
    };

    // Initialize carousel
    initializeCarousel();

    // Set up a mutation observer to watch for changes to the carousel
    const observer = new MutationObserver(() => {
      // Clear existing interval
      if (animationInterval) {
        clearInterval(animationInterval);
      }
      // Reinitialize carousel
      initializeCarousel();
    });

    const carouselElement = document.querySelector('.quick-prompts-carousel');
    if (carouselElement) {
      observer.observe(carouselElement, {
        childList: true,
        subtree: true,
        attributes: true
      });
    }

    // Cleanup function
    return () => {
      if (animationInterval) {
        clearInterval(animationInterval);
      }
      observer.disconnect();
    };
  }, [shouldSkipWelcome, isAuthenticated]); // Add dependencies to ensure animation restarts properly

  // =============================================
  // TOKEN MANAGEMENT EFFECTS
  // =============================================
  useEffect(() => {
    const checkInitialTokens = async () => {
      // Let's remove this check since it could be causing resets
      // We'll rely on the useUserLimits hook for this
      // const { data: { session } } = await supabase.auth.getSession();
      // if (session?.user?.id) {
      //   await checkAndUpdateTokens(session.user.id);
      // }
    };
    checkInitialTokens();
  }, []);

  // =============================================
  // SESSION MANAGEMENT EFFECT
  // =============================================
  useEffect(() => {
    const initializeSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (session?.refresh_token) {
        // Update the access token
        return session.access_token;
      }
    };

    initializeSession();
  }, []);

  // =============================================
  // TOKEN MANAGEMENT FUNCTIONS
  // =============================================
  const checkAndUpdateTokens = async (userId: string) => {
    // This function is now replaced by the useUserLimits hook
    return await hasReachedLimit() === false;
  };

  const consumeToken = async (userId: string) => {
    // This function is now replaced by the useUserLimits hook's incrementMessagesUsed function
    const result = await incrementMessagesUsed();
    return result.success;
  };

  // =============================================
  // SESSION REFRESH FUNCTION
  // =============================================
  const refreshSession = async () => {
    try {
      const { data, error } = await supabase.auth.refreshSession();

      if (error) {
        throw error;
      }

      if (data.session) {
        // Update the access token
        return data.session.access_token;
      }
    } catch (error) {
      // Handle failed refresh by redirecting to login
      navigate('/login');
    }
  };

  // =============================================
  // MESSAGE SUBMISSION HANDLER
  // =============================================
  const queryClient = useQueryClient();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Prevent free users from sending messages
    if (planType === 'free') {
      navigate('/subscription/manage');
      return;
    }

    if (!message.trim() || isLoading) return;

    // STRICT MESSAGE LIMIT ENFORCEMENT
    // Always check with the database before trying to send
    const limitReached = await hasReachedLimit();
    if (limitReached) {
      toast({
        title: "Message limit reached",
        description: `You've reached your ${messagesLimit} message limit for your ${planType} plan. Please upgrade for more messages.`,
        variant: "destructive"
      });
      return; // Prevent message sending
    }

    try {
      setIsLoading(true);

      // Add user message to the chat immediately for better UX
      const userMessage: Message = {
        role: 'user',
        content: {
          text: message
        }
      };

      // Add a loading message
      const loadingMessage: Message = {
        role: 'assistant',
        content: {
          text: null,
          loadingPlaceholder: true,
          isLoading: true
        }
      };

      // Store user input and clear the input field
      setMessages(prev => [...prev, userMessage, loadingMessage]);
      const userInput = message;
      setMessage('');

      // Decrement the counter using the hook - this now handles all database updates
      const result = await incrementMessagesUsed();

      if (!result?.success) {
        toast({
          title: "Message limit error",
          description: result?.message || "Failed to process your message. Please try again.",
          variant: "destructive"
        });
        setIsLoading(false);

        // Remove the loading message
        setMessages(prev => prev.filter(msg => !msg.content.loadingPlaceholder));
        return;
      }

      // Show warning if this was their last message
      if (result.isWarning && result.message) {
        toast({
          title: "Message limit warning",
          description: result.message,
          variant: "default"
        });
      }

      // Continue with the rest of your existing code for sending the message
      try {
        await refreshSession();

        // Make the API call to chat-ai
        const { data: response, error: marketError } = await supabase.functions.invoke<ChatAIResponse>('chat-ai', {
          body: {
            messages: [{
              role: 'user',
              content: userInput
            }]
          }
        });

        // If there was an error with the API call, throw it
        if (marketError) {
          throw new Error(`API Error: ${marketError.message}`);
        }

        if (!response) {
          throw new Error('No response received from chat-ai function');
        }

        // Once we have the complete response, update the message with all data
        setMessages(prev => {
          const updated = [...prev];
          const lastMessage = updated[updated.length - 1];
          if (lastMessage.role === 'assistant') {
            lastMessage.content = {
              text: '',
              marketData: response.marketData,
              symbols: response.symbols,
              symbolTypes: response.symbolTypes,
              isGeneralMessage: response.isGeneralMessage,
              isLoading: false,
              loadingPlaceholder: false
            };
          }
          return updated;
        });
      } catch (error) {
        // Show error but keep the message to avoid losing it
        toast({
          title: 'API Error',
          description: error instanceof Error ? error.message : 'Failed to get a response. Please try again.',
          variant: 'destructive'
        });

        // Update messages to show the error instead of removing the message completely
        setMessages(prev => {
          const updated = [...prev];
          const lastMessage = updated[updated.length - 1];
          if (lastMessage.role === 'assistant') {
            lastMessage.content = {
              text: 'Sorry, I encountered an error processing your request. Please try again.',
              isLoading: false,
              loadingPlaceholder: false,
              isGeneralMessage: true
            };
          }
          return updated;
        });
      }

      setIsLoading(false);
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to get a response. Please try again.',
        variant: 'destructive'
      });
      setIsLoading(false);

      // Remove the loading message on error
      setMessages(prev => prev.filter(msg => !msg.content.loadingPlaceholder));
    }
  };

  // =============================================
  // FILE UPLOAD HANDLER
  // =============================================
  // Removed file upload handler
  // const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   if (e.target.files && e.target.files.length > 0) {
  //     const file = e.target.files[0];
  //     const reader = new FileReader();
  //     reader.onload = (event) => {
  //       // setUploadedImage(event.target?.result as string);
  //     };
  //     reader.readAsDataURL(file);
  //   }
  // };

  // =============================================
  // EFFECTS
  // =============================================
  // Add animation styles effect
  useEffect(() => {
    // Add animation styles to document
    const styleEl = document.createElement('style');
    styleEl.textContent = animationStyles;
    document.head.appendChild(styleEl);

    return () => {
      document.head.removeChild(styleEl);
    };
  }, []);

  // Add an effect to get user's name from profile when component mounts
  useEffect(() => {
    const getUserName = async () => {
      try {
        // Get the current user
        const { data: { user } } = await supabase.auth.getUser();
        if (user?.id) {
          // First try to get the name from the profiles table
          const { data: profile } = await supabase
            .from('profiles')
            .select('full_name')
            .eq('id', user.id)
            .single();

          let firstName = '';

          if (profile?.full_name) {
            // Extract first name from full name
            firstName = profile.full_name.split(' ')[0];
          } else if (user.user_metadata?.full_name) {
            // Try to get from user metadata
            firstName = user.user_metadata.full_name.split(' ')[0];
          } else if (user.email) {
            // Fallback to email username
            firstName = user.email.split('@')[0];
            // Capitalize first letter
            firstName = firstName.charAt(0).toUpperCase() + firstName.slice(1);
          }

          // Only set if we have a valid name (at least 2 chars)
          if (firstName && firstName.length >= 2) {
            setUserName(firstName);
          }
        }
      } catch (error) {
        console.error("Error getting user's name:", error);
      }
    };

    getUserName();
  }, []);

  // =============================================
  // RENDER UI
  // =============================================
  return (
    <div className="relative flex flex-col h-full">
      <style>{animationStyles}</style>

      {/* Add the banner at the top of the page */}
      <TopBanner />

      {/* Show paywall for free users */}
      {planType === 'free' && (
        <div className="absolute inset-0 z-50 flex items-center justify-center bg-[#0A0A0A]/95 backdrop-blur-sm">
          <div className="max-w-md p-8 text-center space-y-4">
            <h2 className="text-2xl font-medium text-white/90">Upgrade to Start Chatting</h2>
            <p className="text-white/60">
              Get unlimited AI-powered market analysis and insights with our Pro plan.
            </p>
            <button
              onClick={() => navigate('/subscription/manage')}
              className="w-full h-11 bg-emerald-500/[0.15] text-emerald-300 rounded-md border border-emerald-500/10 hover:bg-emerald-500/20 transition-all duration-200"
            >
              View Plans
            </button>
          </div>
        </div>
      )}

      {/* Rest of the chat interface */}
      <div className="fixed inset-0 mt-12 flex bg-[#0A0A0A]">
        <div className="flex-1 flex flex-col pt-4 max-h-1000 overflow-hidden">
          {((!currentChatId || currentChatId === 'new') && !isLoading && messages.length === 0 && !shouldSkipWelcome) ? (
            <div className="flex-1 flex items-center justify-center p-5 overflow-hidden">
              <div className="max-w-xl w-full space-y-3 mt-[-20px]">
                {/* Welcome Header */}
                <div className="space-y-3 text-center">
                  <WelcomeHeading
                    text={isNewUser ? "Welcome to Osis! How can I help?" : `What are we analyzing today, ${userName || "trader"}?`}
                    speed={80}
                    className="mx-auto"
                  />
                </div>

                {/* Input Card - Updated with exact styling from the image */}
                <Card
                  className="bg-[#141414]/40 backdrop-filter backdrop-blur-xl border-[0.5px] border-[#303035]/60 rounded-xl shadow-[0_8px_16px_rgba(0,0,0,0.2)] relative p-2 cursor-text"
                  onClick={() => {
                    const textareaElement = document.querySelector('textarea[placeholder="Ask away..."]') as HTMLTextAreaElement;
                    if (textareaElement) textareaElement.focus();
                  }}
                >
                  <div className="flex flex-col min-h-[36px]">
                    <form onSubmit={handleSubmit} className="relative flex flex-col">
                      {/* Textarea that grows with content - Now taller by default */}
                      <div className="relative">
                        <textarea
                          value={message}
                          onChange={(e) => {
                            setMessage(e.target.value);
                            // Auto-resize the textarea
                            e.target.style.height = '40px';
                            e.target.style.height = `${Math.min(120, Math.max(40, e.target.scrollHeight))}px`;
                          }}
                          placeholder="Ask away..."
                          className="bg-transparent border-0 outline-none text-[1rem] text-white placeholder:text-white/40 w-full resize-none overflow-hidden pl-3 pt-2.5 pb-16 pr-[100px] focus:outline-none focus:ring-0 focus:border-0 focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
                          rows={2}
                          style={{ height: '100px' }} // Increased height to accommodate lower button
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                              e.preventDefault();
                              handleSubmit(e as any);
                            }
                          }}
                        />
                      </div>

                      {/* Right Controls - Submit button fixed to the bottom right */}
                      <div className="button-container welcome-button">
                        <button
                          type="submit"
                          disabled={isLoading || !message.trim()}
                          className="bg-[#1A1A1C] text-white/90 border border-[#232323]/80 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.1)] rounded-full px-3 py-0.5 h-8 text-xs flex items-center justify-center overflow-hidden"
                        >
                          {isLoading ? (
                            <>
                              <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                              <span className="text-[10px]">Processing...</span>
                            </>
                          ) : (
                            <>
                              <ArrowUp className="w-3 h-3 mr-0.5" />
                              <span className="text-[10px]">Submit</span>
                            </>
                          )}
                        </button>
                      </div>
                    </form>
                  </div>
                </Card>

                {/* Quick Prompts Carousel - Enhanced with sophisticated styling */}
                <div className="relative mt-4">
                  {/* Gradient underlays for depth */}
                  <div className="absolute inset-x-0 h-12 top-1/2 -translate-y-1/2 opacity-30">
                    <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-purple-500/5 to-blue-500/5"></div>
                    <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-white/[0.03] via-transparent to-transparent"></div>
                  </div>

                  <div className="flex overflow-hidden py-1.5">
                    <div
                      className="flex gap-2 quick-prompts-carousel px-1"
                      style={{
                        transform: `translateX(${slidePosition}px)`,
                        transition: slidePosition === 0 ? 'none' : 'transform 0.1s linear',
                        willChange: 'transform'
                      }}
                    >
                      {[...QUICK_PROMPTS, ...QUICK_PROMPTS].map((prompt, index) => (
                        <button
                          key={index}
                          onClick={() => {
                            setMessage(prompt.text);
                            const syntheticEvent = new Event('submit', {
                              bubbles: true,
                              cancelable: true,
                            }) as unknown as React.FormEvent;
                            handleSubmit(syntheticEvent);
                          }}
                          className="group relative flex items-center gap-2 px-3 py-1.5 rounded-lg bg-[#141414]/40 hover:bg-[#1A1A1A]/60 transition-all duration-300 border border-white/[0.02] hover:border-white/[0.05] shadow-[0_0_1px_rgba(0,0,0,0.1)] backdrop-blur-sm whitespace-nowrap"
                        >
                          {/* Hover effect overlay */}
                          <div className="absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div className="absolute inset-0 bg-gradient-to-r from-white/[0.02] to-transparent"></div>
                          </div>

                          {/* Category-specific styling */}
                          {prompt.category === "Finance" && (
                            <div className="relative flex items-center justify-center w-5 h-5">
                              <div className="absolute inset-0 rounded-md rotate-45 bg-emerald-950/30 group-hover:bg-emerald-900/40 transition-colors duration-300"></div>
                              <TrendingUp className="w-3 h-3 text-emerald-300/70 group-hover:text-emerald-200/90 transition-all duration-300 relative z-10 transform group-hover:scale-110" />
                            </div>
                          )}

                          {prompt.category === "Crypto" && (
                            <div className="relative flex items-center justify-center w-5 h-5">
                              <div className="absolute inset-0 rounded-md rotate-45 bg-orange-950/30 group-hover:bg-orange-900/40 transition-colors duration-300"></div>
                              <Bitcoin className="w-3 h-3 text-orange-300/70 group-hover:text-orange-200/90 transition-all duration-300 relative z-10 transform group-hover:scale-110" />
                            </div>
                          )}

                          {prompt.category === "News" && (
                            <div className="relative flex items-center justify-center w-5 h-5">
                              <div className="absolute inset-0 rounded-md rotate-45 bg-blue-950/30 group-hover:bg-blue-900/40 transition-colors duration-300"></div>
                              <Newspaper className="w-3 h-3 text-blue-300/70 group-hover:text-blue-200/90 transition-all duration-300 relative z-10 transform group-hover:scale-110" />
                            </div>
                          )}

                          {prompt.category === "Education" && (
                            <div className="relative flex items-center justify-center w-5 h-5">
                              <div className="absolute inset-0 rounded-md rotate-45 bg-purple-950/30 group-hover:bg-purple-900/40 transition-colors duration-300"></div>
                              <BookOpen className="w-3 h-3 text-purple-300/70 group-hover:text-purple-200/90 transition-all duration-300 relative z-10 transform group-hover:scale-110" />
                            </div>
                          )}

                          {prompt.category === "Investment" && (
                            <div className="relative flex items-center justify-center w-5 h-5">
                              <div className="absolute inset-0 rounded-md rotate-45 bg-cyan-950/30 group-hover:bg-cyan-900/40 transition-colors duration-300"></div>
                              <PieChart className="w-3 h-3 text-cyan-300/70 group-hover:text-cyan-200/90 transition-all duration-300 relative z-10 transform group-hover:scale-110" />
                            </div>
                          )}

                          {/* Text with gradient underline effect */}
                          <span className="relative text-[13px] font-medium text-white/60 group-hover:text-white/90 transition-colors duration-300">
                            {prompt.text}
                            <span className="absolute bottom-0 left-0 w-0 h-[1px] bg-gradient-to-r from-white/20 to-transparent group-hover:w-full transition-all duration-500"></span>
                          </span>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Enhanced fade-out effect */}
                  <div className="absolute inset-y-0 right-0 w-32 pointer-events-none">
                    <div className="absolute inset-0 bg-gradient-to-l from-[#0A0A0A] via-[#0A0A0A]/95 to-transparent"></div>
                    <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,_var(--tw-gradient-stops))] from-transparent via-[#0A0A0A]/20 to-transparent opacity-50"></div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            /* Chat Message Display - shown when messages exist */
            <div className="flex-1 flex flex-col overflow-hidden">
              {/* Message Area with Scrollbar */}
              <ScrollArea className="flex-1 px-4 py-4">
                <div className="max-w-4xl mx-auto space-y-6">
                  {messages.map((msg, index) => (
                    <div
                      key={index}
                      className="animate-pop-in"
                      style={{
                        animationDelay: `${index * 100}ms`,
                        animationFillMode: 'both',
                        marginBottom: '-20px'
                      }}
                    >
                      {msg.role === 'user' ? (
                        <div className="text-lg font-bold text-white/95 mb-1">
                          {msg.content.text}
                        </div>
                      ) : msg.content.loadingPlaceholder ? (
                        <div className="mt-6 space-y-4">
                          <div className="flex flex-col space-y-4 pb-4">
                            <div className="text-white/70 flex items-center gap-2">
                              <div className="h-4 w-4 rounded-full border-2 border-white/10 border-t-white/30 animate-spin" />
                              Analyzing your request...
                            </div>
                          </div>
                        </div>
                      ) : (
                        <LoadingMessage
                          userQuery={messages[index - 1]?.content?.text || ""}
                          isComplete={!msg.content.isLoading}
                          symbols={msg.content.symbols}
                          finalContent={msg.content.aiAnalysis || msg.content.text}
                          marketData={msg.content.marketData}
                          isLoading={msg.content.isLoading}
                          symbolTypes={msg.content.symbolTypes}
                          isGeneralMessage={msg.content.isGeneralMessage}
                          onRegenerate={async () => {
                            // Get the user query from the previous message
                            const userQuery = messages[index - 1]?.content?.text || "";
                            if (!userQuery) return;

                            // Decrement the counter using the hook - this counts as a message
                            const result = await incrementMessagesUsed();

                            if (!result?.success) {
                              toast({
                                title: "Message limit error",
                                description: result?.message || "Failed to regenerate. Please try again.",
                                variant: "destructive"
                              });
                              return;
                            }

                            try {
                              // Make the API call to chat-ai with the same user query
                              const { data: response, error: marketError } = await supabase.functions.invoke<ChatAIResponse>('chat-ai', {
                                body: {
                                  messages: [{
                                    role: 'user',
                                    content: userQuery
                                  }]
                                }
                              });

                              if (marketError) {
                                throw new Error(`API Error: ${marketError.message}`);
                              }

                              if (!response) {
                                throw new Error('No response received from chat-ai function');
                              }

                              // Update this specific message with the new response
                              setMessages(prev => {
                                const updated = [...prev];
                                if (updated[index]) {
                                  updated[index].content = {
                                    ...updated[index].content,
                                    marketData: response.marketData,
                                    symbols: response.symbols,
                                    symbolTypes: response.symbolTypes,
                                    isGeneralMessage: response.isGeneralMessage,
                                    isLoading: false,
                                    loadingPlaceholder: false
                                  };
                                }
                                return updated;
                              });
                            } catch (error) {
                              toast({
                                title: 'Regeneration Error',
                                description: error instanceof Error ? error.message : 'Failed to regenerate response. Please try again.',
                                variant: 'destructive'
                              });
                            }
                          }}
                        />
                      )}
                    </div>
                  ))}
                </div>
              </ScrollArea>

              {/* Input Area - Keep only this one message box */}
              <div className="border-t border-white/0 p-2 sm:p-4">
                <div className="max-w-3xl mx-auto">
                  <div className="relative">
                    <div className="relative">
                      <textarea
                        ref={fileInputRef}
                        className="w-full min-h-[100px] bg-[#141414]/40 backdrop-filter backdrop-blur-xl border-[0.5px] border-[#303035]/60 rounded-xl text-white placeholder:text-white/40 resize-none p-4 pb-8 pr-[100px] focus:outline-none focus:ring-0 focus:border-[#303035]/40 focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 shadow-[0_8px_16px_rgba(0,0,0,0.2)]"
                        value={message}
                        onChange={(e) => {
                          setMessage(e.target.value);
                          // Auto-resize the textarea with smaller max height on mobile
                          e.target.style.height = '40px';
                          const maxHeight = window.innerWidth < 640 ? 100 : 200;
                          e.target.style.height = `${Math.min(maxHeight, Math.max(40, e.target.scrollHeight))}px`;
                        }}
                        placeholder="Ask away..."
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            handleSubmit(e as any);
                          }
                        }}
                        rows={1}
                        style={{ height: '100px' }}
                      />
                      <div className="button-container">
                        <button
                          type="submit"
                          disabled={isLoading || !message.trim()}
                          className="bg-[#1A1A1C] text-white/90 border border-[#232323]/80 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.1)] rounded-full px-3 py-0.5 h-8 text-xs flex items-center mb-50 justify-center overflow-hidden"
                          onClick={handleSubmit}
                        >
                          {isLoading ? (
                            <>
                              <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                              <span className="text-[10px]">Processing...</span>
                            </>
                          ) : (
                            <>
                              <ArrowUp className="w-3 h-3 mr-0.5" />
                              <span className="text-[10px]">Submit</span>
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <style>
        {`
          textarea:focus-visible {
            outline: none !important;
            border-color: #303035 !important;
            box-shadow: none !important;
            ring: 0 !important;
          }
          textarea:focus {
            outline: none !important;
            border-color: #303035 !important;
            box-shadow: none !important;
            ring: 0 !important;
          }

          /* Add subtle transition effects */
          textarea, button {
            transition: all 0.2s ease;
          }

          /* Ensure button container stays within the input box */
          .button-container {
            position: absolute;
            bottom: 16px;
            right: 8px;
            z-index: 10;
            max-width: calc(100% - 16px);
            max-height: calc(100% - 32px);
          }

          /* Additional spacing for welcome screen button */
          .welcome-button {
            bottom: 7px;
          }

          /* Ensure button text doesn't overflow */
          .button-container button {
            max-width: 100%;
            white-space: nowrap;
            overflow: hidden;
          }

          /* Mobile specific adjustments */
          @media (max-width: 640px) {
            .button-container {
              bottom: 18px;
            }

            .welcome-button {
              bottom: 0px;
            }

            .button-container button {
              padding-left: 8px;
              padding-right: 8px;
            }

            .button-container button span {
              font-size: 9px;
            }
          }

          /* Darker placeholder text on focus */
          textarea:focus::placeholder {
            color: rgba(255, 255, 255, 0.2);
          }
        `}
      </style>
    </div>
  );
};

// Update the TopBanner component styling to match the pricing card
const TopBanner = () => {
  const [isVisible, setIsVisible] = useState(true);
  const navigate = useNavigate();
  const { id: currentChatId } = useParams();

  // Show the banner on every page refresh, but respect session-based dismissal
  useEffect(() => {
    // Use sessionStorage instead of localStorage to make banner reappear on refresh
    const dismissed = sessionStorage.getItem('portfolioBannerDismissed') === 'true';

    // Always show on main page or new chat, unless dismissed in current session
    if (!currentChatId || currentChatId === 'new') {
      setIsVisible(!dismissed);
    } else {
      // On specific chat pages, check if dismissed
      setIsVisible(!dismissed);
    }

    // Record that banner was shown in this session
    const now = new Date().getTime();
    sessionStorage.setItem('portfolioBannerLastShown', now.toString());
  }, [currentChatId]);

  // Check if we're currently in a loading state or showing a response
  const [isResponsePage, setIsResponsePage] = useState(false);

  useEffect(() => {
    // Function to check if we're on a response page or loading a response
    const checkIfResponsePage = () => {
      // Check for various indicators that we're on a response page
      // 1. Look for loading indicators
      const loadingIndicators = document.querySelectorAll('.typing-indicator, [aria-label="Regenerate response"]');

      // 2. Look for user messages or AI responses
      const messageElements = document.querySelectorAll('.animate-pop-in, .animate-reveal');

      // 3. Look for specific text that indicates we're on a response page
      const textContent = document.body.textContent || '';

      // Only check for specific loading/response indicators, not user input
      const hasAnalyzingText = textContent.includes('Analyzing your request') ||
                              textContent.includes('Loading analysis');

      // Set state based on all these checks
      setIsResponsePage(
        loadingIndicators.length > 0 ||
        messageElements.length > 0 ||
        hasAnalyzingText
      );
    };

    // Check immediately
    checkIfResponsePage();

    // Set up a mutation observer to detect DOM changes
    const observer = new MutationObserver(checkIfResponsePage);
    observer.observe(document.body, { childList: true, subtree: true, characterData: true });

    return () => observer.disconnect();
  }, []);

  // Don't show the banner if not visible or if we're on a response page
  if (!isVisible || isResponsePage) return null;

  return (
    <div className="fixed top-[140px] md:top-24 left-1/2 transform -translate-x-1/2 z-40 animate-fade-in w-[90%] max-w-md">
      <div
        className="rounded-2xl overflow-hidden relative w-full"
        style={{
          background: "linear-gradient(to right, #121212, #181818)",
          boxShadow: "0 4px 15px rgba(0,0,0,0.3), 0 0 0 1px rgba(255,255,255,0.05)"
        }}
      >
        {/* Inner shadow overlay */}
        <div
          className="absolute inset-0 pointer-events-none"
          style={{
            boxShadow: "inset 0 1px 3px rgba(0,0,0,0.3), inset 0 0 2px rgba(0,0,0,0.2)"
          }}
        ></div>

        {/* Subtle top highlight */}
        <div className="absolute top-0 left-0 right-0 h-[1px] bg-white/5"></div>

        <div className="flex items-center p-3 flex-wrap sm:flex-nowrap">
          {/* Left side with icon and text */}
          <div className="flex items-center flex-1 min-w-0">
            <div className="w-7 h-7 rounded-lg flex items-center justify-center mr-3 overflow-hidden relative flex-shrink-0">
              {/* Icon container with subtle style */}
              <div
                className="relative z-10 rounded-md w-full h-full flex items-center justify-center"
                style={{
                  background: "rgba(30, 30, 30, 0.8)"
                }}
              >
                <PieChart className="w-3.5 h-3.5 text-white/80" />
              </div>
            </div>

            <div className="min-w-0">
              <h3 className="text-sm font-medium text-white/90 flex items-center">
                <span className="text-white mr-1.5">Create Your Portfolio</span>
              </h3>
              <p className="text-[10px] text-white/50 truncate">Start investing with AI-optimized assets</p>
            </div>
          </div>

          {/* Right side with actions */}
          <div className="flex items-center ml-auto mt-1 sm:mt-0 sm:ml-4">
            <button
              onClick={() => navigate('/portfolio-builder')}
              className="text-xs px-4 py-2 rounded-md mr-2 transition-all duration-200 whitespace-nowrap text-black font-medium hover:opacity-90 min-w-[60px] min-h-[36px]"
              style={{
                background: "linear-gradient(to bottom, rgba(255,255,255,1), rgba(240,240,240,1))",
                boxShadow: "0 1px 2px rgba(0,0,0,0.1)"
              }}
            >
              Try it
            </button>

            <button
              onClick={() => {
                setIsVisible(false);
                sessionStorage.setItem('portfolioBannerDismissed', 'true');
              }}
              className="text-white/40 hover:text-white/70 p-2 rounded-full hover:bg-white/5 transition-colors duration-200 min-w-[32px] min-h-[32px]"
              aria-label="Dismiss"
            >
              <X size={14} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
