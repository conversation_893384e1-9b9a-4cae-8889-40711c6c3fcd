import React from 'react';
import ReactECharts from 'echarts-for-react';
import { Card } from "@/components/ui/card";

interface ChartDataPoint {
  date: string;
  value: number;
}

interface PortfolioChartProps {
  data: ChartDataPoint[];
  title?: string;
  height?: number;
  loading?: boolean;
}

const PortfolioChart: React.FC<PortfolioChartProps> = ({
  data,
  title = 'Portfolio Performance',
  height = 300,
  loading = false
}) => {
  if (loading) {
    return (
      <Card className="w-full h-[300px] flex items-center justify-center bg-muted/20">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-8 w-8 rounded-full border-2 border-t-transparent border-white/30 animate-spin"></div>
          <p className="mt-2 text-sm text-muted-foreground">Loading chart data...</p>
        </div>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card className="w-full h-[300px] flex items-center justify-center bg-muted/20">
        <p className="text-muted-foreground">No data available</p>
      </Card>
    );
  }

  const dates = data.map(item => item.date);
  const values = data.map(item => item.value);

  // Calculate starting value and current value for percentage change
  const startValue = values[0];
  const currentValue = values[values.length - 1];
  const percentChange = ((currentValue - startValue) / startValue) * 100;

  // Calculate min and max values for y-axis scaling
  const minValue = Math.min(...values) * 0.9; // 10% below minimum
  const maxValue = Math.max(...values) * 1.1; // 10% above maximum

  // Filter dates to show fewer x-axis labels (show only every 24th month = every 2 years)
  const filteredDates = dates.filter((_, index) => index % 24 === 0);
  const filteredIndices = filteredDates.map(date => dates.indexOf(date));

  const option = {
    title: {
      text: title,
      left: 'center',
      textStyle: {
        color: '#fff',
        fontWeight: 'normal',
        fontSize: 16
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        const dataIndex = params[0].dataIndex;
        const date = dates[dataIndex];
        const value = values[dataIndex].toFixed(2);
        const initialValue = values[0];
        const percentChange = (((values[dataIndex] - initialValue) / initialValue) * 100).toFixed(2);

        // Format date to be more readable
        const formattedDate = new Date(date).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short'
        });

        return `
          <div style="font-size:14px;color:#fff;font-weight:500;margin-bottom:7px">${formattedDate}</div>
          <div style="font-size:13px;color:#fff;font-weight:400">
            <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#5470c6"></span>
            Value: $${value}
          </div>
          <div style="font-size:13px;color:#fff;font-weight:400">
            <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${percentChange >= 0 ? '#91cc75' : '#ee6666'}"></span>
            Change: ${percentChange}%
          </div>
        `;
      },
      backgroundColor: 'rgba(20, 20, 20, 0.9)',
      borderColor: 'rgba(255, 255, 255, 0.1)',
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '60px',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.2)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        interval: function(index: number) {
          // Only show labels for specific years (every 2 years)
          return filteredIndices.includes(index);
        },
        formatter: function(value: string) {
          // Format date to show only year
          const date = new Date(value);
          return date.getFullYear().toString();
        }
      },
      axisPointer: {
        label: {
          formatter: function(params: any) {
            const date = new Date(params.value);
            return `${date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}`;
          }
        }
      }
    },
    yAxis: {
      type: 'value',
      min: minValue, // Set minimum value for more vertical stretch
      max: maxValue, // Set maximum value for more vertical stretch
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.2)'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        formatter: function(value: number) {
          return '$' + value.toLocaleString();
        }
      }
    },
    series: [
      {
        data: values,
        type: 'line',
        smooth: true,
        symbol: 'none',
        lineStyle: {
          width: 3,
          color: percentChange >= 0 ? 'rgba(78, 184, 151, 0.9)' : 'rgba(229, 128, 128, 0.9)'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: percentChange >= 0 ? 'rgba(78, 184, 151, 0.4)' : 'rgba(229, 128, 128, 0.4)'
            }, {
              offset: 0.7,
              color: percentChange >= 0 ? 'rgba(78, 184, 151, 0.1)' : 'rgba(229, 128, 128, 0.1)'
            }, {
              offset: 1,
              color: 'rgba(0, 0, 0, 0)'
            }]
          }
        }
      }
    ]
  };

  return (
    <ReactECharts
      option={option}
      style={{ height: `${height}px`, width: '100%' }}
      className="bg-[#0D0D0D] rounded-md"
    />
  );
};

export default PortfolioChart;
