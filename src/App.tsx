import React, { useState, useEffect } from "react";
import { setRefreshTokenFunction } from "./utils/tokenUtils";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from "react-router-dom";
import ChatInterface from "./components/chat/ChatInterface";
import { MainLayout } from "./components/layout/MainLayout";
import { supabase } from "./integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import Settings from './pages/Settings';
import About from './pages/About';
import ManageSubscription from './pages/ManageSubscription';
import Subscription from './pages/Subscription';
import ModelSettings from './pages/ModelSettings';
import Trades from './pages/Trades';
import PortfolioManager from './pages/PortfolioManager';
import PortfolioNews from './pages/PortfolioNews';
import ApiTest from './pages/ApiTest';
import AgentBuilder from './pages/AgentBuilder';
import AgentManagement from './pages/AgentManagement';
import AgentScanner from './pages/AgentScanner';
import AgentBacktesting from './pages/AgentBacktesting';
import AuthModal from './components/auth/AuthModal';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { WhopProvider, useWhop } from './contexts/WhopContext';
import WhopCallback from './pages/WhopCallback';
import TermsOfService from './pages/TermsOfService';
import PrivacyPolicy from './pages/PrivacyPolicy';
import { cn } from "@/lib/utils";
import Onboarding from './components/Onboarding';
// Login component removed - using AuthModal instead
import { AnimatePresence } from "framer-motion";
import LandingPage from './pages/LandingPage';
import { getSubscriptionCookie, setSubscriptionCookie } from './utils/cookieUtils';
import { setSelectedPlanType, PLAN_TYPES } from './utils/planUtils';
import VideoPopup from './components/VideoPopup';
import { useVideoPopup } from './hooks/useVideoPopup';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      staleTime: 5 * 60 * 1000,
      refetchOnWindowFocus: true,
      refetchOnReconnect: true,
    },
  },
});

// Protected route component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated } = useAuth();
  const { isWhopUser } = useWhop();
  const location = useLocation();
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  useEffect(() => {
    // Give a short delay to ensure auth state is loaded
    const timer = setTimeout(() => {
      setIsCheckingAuth(false);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  // Show loading state while checking authentication
  if (isCheckingAuth) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white/30"></div>
      </div>
    );
  }

  // Redirect if not authenticated after checking
  if (!isAuthenticated && !isWhopUser) {
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

// Create a wrapper component to handle the auth modal logic
const AppContent = () => {
  const { isAuthenticated, user, refreshToken } = useAuth();
  const { isWhopUser } = useWhop();
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const location = useLocation();
  const { showVideoPopup, closeVideoPopup } = useVideoPopup();

  // Set the refreshToken function from AuthContext
  useEffect(() => {
    if (refreshToken) {
      console.log('[App] Setting refreshToken function');
      setRefreshTokenFunction(refreshToken);
    }
  }, [refreshToken]);

  // Define paths where auth modal should not appear
  const noAuthModalPaths = ['/terms', '/privacy', '/subscription', '/subscription/manage'];

  // Check authentication status
  const isUserAuthenticated = isAuthenticated || isWhopUser;

  // Start with landing page shown and onboarding hidden
  const [hasSeenOnboarding, setHasSeenOnboarding] = useState(false);
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [isCheckingOnboarding, setIsCheckingOnboarding] = useState(false);

  // Function to check onboarding status
  const checkOnboardingStatus = async () => {
    // Check for URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const subscriptionSuccess = urlParams.get('subscription_success') === 'true';
    const subscriptionPending = urlParams.get('subscription_pending') === 'true';
    const sessionId = urlParams.get('session_id');
    const success = urlParams.get('success') === 'true';
    const customerId = urlParams.get('customer_id');
    const canceled = urlParams.get('canceled') === 'true';

    // Check for plan selection parameters
    const basicPlan = urlParams.get('basic') !== null;
    const proPlan = urlParams.get('pro') !== null;
    const basicNoTrial = urlParams.get('basicnotrial') !== null;
    const proNoTrial = urlParams.get('pronotrial') !== null;

    // Store the selected plan type if present in URL
    if (basicPlan || basicNoTrial) {
      setSelectedPlanType(PLAN_TYPES.BASIC);
      // Clean up URL but preserve other parameters
      const newUrl = new URL(window.location.href);
      if (basicPlan) newUrl.searchParams.delete('basic');
      // Don't remove the basicnotrial parameter as we need it for checkout
      window.history.replaceState({}, document.title, newUrl.toString());
    } else if (proPlan || proNoTrial) {
      setSelectedPlanType(PLAN_TYPES.PRO);
      // Clean up URL but preserve other parameters
      const newUrl = new URL(window.location.href);
      if (proPlan) newUrl.searchParams.delete('pro');
      // Don't remove the pronotrial parameter as we need it for checkout
      window.history.replaceState({}, document.title, newUrl.toString());
    }

    // Note: We don't automatically close onboarding when canceled=true is present
    // as it can be present for both paid and unpaid users

    // If we have a session_id and success=true, this is a Stripe redirect
    if (sessionId && success && isAuthenticated && user) {
      try {
        // Handle the checkout redirect
        const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
          },
          body: JSON.stringify({
            action: 'handle-checkout-redirect',
            sessionId,
            customerId
          })
        });

        const { session, error } = await response.json();

        if (!error && session) {
          // Update the database
          await supabase
            .from('profiles')
            .update({
              has_seen_onboarding: true,
              updated_at: new Date().toISOString()
            })
            .eq('id', user.id);

          // Immediately close the onboarding popup
          setHasSeenOnboarding(true);
          setShowOnboarding(false);

          // Clean up URL
          window.history.replaceState({}, document.title, window.location.pathname);

          // If session was processed successfully, no need to continue with the rest of the checks
          return;
        }
      } catch (error) {
        console.error('Error handling Stripe redirect:', error);
      }
    }

    // If we detect successful subscription, mark onboarding as seen and close it immediately
    if (subscriptionSuccess && isAuthenticated && user) {
      try {
        // Update the database
        await supabase
          .from('profiles')
          .update({
            has_seen_onboarding: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', user.id);

        // Immediately close the onboarding popup
        setHasSeenOnboarding(true);
        setShowOnboarding(false);

        // Clean up URL
        window.history.replaceState({}, document.title, window.location.pathname);

        // If subscription was successful, no need to continue with the rest of the checks
        return;
      } catch (error) {
        console.error('Error updating subscription success:', error);
      }
    }

    // If we detect pending subscription after OAuth login, show the paywall
    if (subscriptionPending && isAuthenticated && user) {
      try {
        // Clean up URL but keep the onboarding open
        window.history.replaceState({}, document.title, window.location.pathname);

        // Force show the onboarding with paywall
        setHasSeenOnboarding(false);
        setShowOnboarding(true);
        return;
      } catch (error) {
        console.error('Error handling subscription pending:', error);
      }
    }

    // Only check onboarding status if user is authenticated
    if (isAuthenticated && user) {
      try {
        setIsCheckingOnboarding(true);

        // First check if the user profile exists
        const { data: profileExists, error: profileCheckError } = await supabase
          .from('profiles')
          .select('id')
          .eq('id', user.id)
          .maybeSingle();

        // If profile doesn't exist, create it
        if (!profileExists) {
          const { error: createError } = await supabase
            .from('profiles')
            .insert({
              id: user.id,
              subscription_type: null,
              has_seen_onboarding: false,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (createError) {
            console.error('Error creating profile:', createError);
          }
        }

        // Now get profile data
        const { data, error } = await supabase
          .from('profiles')
          .select('subscription_type, has_seen_onboarding')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Error fetching profile:', error);
          // Default to showing onboarding if there's an error
          setHasSeenOnboarding(false);
          setShowOnboarding(true);
          return;
        }

        // Get subscription status from the subscriptions table
        const { data: subscriptionData } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(1)
          .maybeSingle();

        // First check if the user has an active subscription from subscriptions table
        const hasActiveSubscription =
          subscriptionData &&
          subscriptionData.status &&
          ['active', 'trialing'].includes(subscriptionData.status);

        // Also check subscription_mappings table using user's email
        let hasSubscriptionMapping = false;
        try {
          const userEmail = user.email;
          if (userEmail) {
            const { data: mappingData, error: mappingError } = await supabase
              .from('subscription_mappings')
              .select('*')
              .eq('email', userEmail)
              .maybeSingle();

            if (mappingError) {
              console.error('Error checking subscription_mappings:', mappingError);
            } else {
              // If there's any entry for this email in subscription_mappings, consider them paid
              hasSubscriptionMapping = !!mappingData;
            }
          }
        } catch (mappingError) {
          console.error('Error checking subscription_mappings:', mappingError);
        }

        // User has a subscription if they have an active subscription OR a subscription mapping
        const userHasSubscription = hasActiveSubscription || hasSubscriptionMapping;

        // Update subscription cookie based on database check
        if (userHasSubscription) {
          setSubscriptionCookie(true);
        } else {
          // Clear any existing subscription cookie if user doesn't have a subscription
          setSubscriptionCookie(false);
        }

        // IMPORTANT: We're changing the logic here to ALWAYS show onboarding for users without a subscription
        // regardless of whether they've seen it before
        const hasSeenOnboarding = data?.has_seen_onboarding === true;

        // ONLY consider a user as having seen onboarding if they have an active subscription
        // This ensures users without a subscription always see the onboarding/payment flow
        setHasSeenOnboarding(userHasSubscription);
        setShowOnboarding(!userHasSubscription);
      } catch (error) {
        console.error('Error checking onboarding status:', error);
        // Default to showing onboarding if there's an error
        setHasSeenOnboarding(false);
        setShowOnboarding(true);
      } finally {
        setIsCheckingOnboarding(false);
      }
    }
  };

  useEffect(() => {
    checkOnboardingStatus();
  }, [isAuthenticated, user]);

  // Expose the checkOnboardingStatus function to window for other components to call
  useEffect(() => {
    // @ts-ignore - Adding to window object
    window.refreshOnboardingStatus = checkOnboardingStatus;

    return () => {
      // @ts-ignore - Removing from window object
      delete window.refreshOnboardingStatus;
    };
  }, [isAuthenticated, user]);

  useEffect(() => {
    // Check for auth tokens in the URL (from OAuth redirects)
    const checkForAuthTokens = async () => {
      try {
        // Check if we have an access token in the URL hash (from OAuth)
        const hashParams = new URLSearchParams(window.location.hash.substring(1));
        const accessToken = hashParams.get('access_token');

        if (accessToken) {
          // This will be handled by AuthContext now
        }
      } catch (error) {
      } finally {
        setIsAuthenticating(false);
      }
    };

    checkForAuthTokens();
  }, [location.pathname]);

  // Add visibility change handler
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // Force refresh QueryClient
        queryClient.invalidateQueries();

        // Reconnect Supabase
        supabase.auth.startAutoRefresh();

        // Refresh onboarding status when tab becomes visible
        checkOnboardingStatus();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  const handleCloseOnboarding = async () => {
    // Immediately update UI state
    setShowOnboarding(false);
    setHasSeenOnboarding(true);

    // If user is authenticated, update their profile
    if (isAuthenticated && user) {
      try {
        const { error } = await supabase
          .from('profiles')
          .update({
            has_seen_onboarding: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', user.id);

        if (error) {
          console.error('Error updating onboarding status:', error);
        }
      } catch (error) {
        console.error('Error updating onboarding status:', error);
      }
    }
  };

  // Show landing page first if not authenticated and not showing onboarding
  if (location.pathname === '/' && !isUserAuthenticated && !showOnboarding) {
    return <LandingPage setShowOnboarding={setShowOnboarding} />;
  }

  return (
    <div className={cn(
      "bg-[#0A0A0A]",
      // Only fix position when not on terms/privacy pages
      !noAuthModalPaths.includes(location.pathname) ? "fixed inset-0 overflow-hidden" : "min-h-screen"
    )}>
      {/* Only show MainLayout and Routes if not showing onboarding or if user is authenticated */}
      {(!showOnboarding || isUserAuthenticated) && (
        <MainLayout loading={isCheckingOnboarding}>
          <Routes>
            <Route path="/chat/:chatId" element={
              <ProtectedRoute><ChatInterface /></ProtectedRoute>
            } />

            {/* Protected routes with ProtectedRoute wrapper */}
            <Route path="/settings" element={<ProtectedRoute><Settings /></ProtectedRoute>} />
            <Route path="/model-settings" element={<ProtectedRoute><ModelSettings /></ProtectedRoute>} />
            <Route path="/subscription" element={<ProtectedRoute><Subscription /></ProtectedRoute>} />
            <Route path="/subscription/manage" element={<ProtectedRoute><ManageSubscription /></ProtectedRoute>} />
            <Route path="/trades" element={<ProtectedRoute><Trades /></ProtectedRoute>} />
            <Route path="/portfolio-builder" element={<ProtectedRoute><PortfolioManager /></ProtectedRoute>} />
            <Route path="/portfolio-manager" element={<Navigate to="/portfolio-builder" replace />} />
            <Route path="/portfolio-news" element={<ProtectedRoute><PortfolioNews /></ProtectedRoute>} />
            <Route path="/api-test" element={<ProtectedRoute><ApiTest /></ProtectedRoute>} />
            <Route path="/agents" element={<ProtectedRoute><AgentManagement /></ProtectedRoute>} />
            <Route path="/agent-builder" element={<ProtectedRoute><AgentBuilder /></ProtectedRoute>} />
            <Route path="/agent-builder/:id" element={<ProtectedRoute><AgentBuilder /></ProtectedRoute>} />
            <Route path="/agent-scanner" element={<ProtectedRoute><AgentScanner /></ProtectedRoute>} />
            <Route path="/agent-backtesting" element={<ProtectedRoute><AgentBacktesting /></ProtectedRoute>} />

            <Route path="/about" element={<About />} />
            <Route path="/terms" element={<TermsOfService />} />
            <Route path="/privacy" element={<PrivacyPolicy />} />
            <Route path="/callback/whop" element={<WhopCallback />} />
            <Route path="/login" element={<Navigate to="/" replace />} />
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <ChatInterface />
                </ProtectedRoute>
              }
            />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </MainLayout>
      )}

      <AnimatePresence>
        {showOnboarding && (
          <Onboarding
            isOpen={showOnboarding}
            onClose={handleCloseOnboarding}
            supabase={supabase}
          />
        )}
      </AnimatePresence>

      {/* Video Popup - only show on main page when authenticated and not loading */}
      <AnimatePresence>
        {showVideoPopup && isUserAuthenticated && !isCheckingOnboarding && !showOnboarding && (
          <VideoPopup
            videoUrl="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/videos//signup.mp4"
            onClose={closeVideoPopup}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

const App = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: 1,
        staleTime: 5 * 60 * 1000,
        refetchOnWindowFocus: true,
        refetchOnReconnect: true,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <WhopProvider>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <Router>
              <AppContent />
            </Router>
          </TooltipProvider>
        </WhopProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
};

export default App;
